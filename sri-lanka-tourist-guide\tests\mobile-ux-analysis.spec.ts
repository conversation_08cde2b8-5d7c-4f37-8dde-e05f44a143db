import { test, expect } from '@playwright/test';

test.describe('Mobile UX Analysis - 2025 Standards', () => {
  const pages = [
    { name: 'Home', url: '/' },
    { name: 'Destinations', url: '/destinations' },
    { name: 'Activities', url: '/activities' },
    { name: 'Culture', url: '/culture' },
    { name: 'Festivals', url: '/festivals' },
    { name: 'Articles', url: '/articles' }
  ];

  test.beforeEach(async ({ page }) => {
    // Set mobile viewport (375px width - iPhone SE standard)
    await page.setViewportSize({ width: 375, height: 812 });
    
    // Set generous timeout for animations and loading
    page.setDefaultTimeout(15000);
  });

  for (const pageInfo of pages) {
    test(`Mobile Analysis: ${pageInfo.name} Page`, async ({ page }) => {
      console.log(`\n=== ANALYZING ${pageInfo.name.toUpperCase()} PAGE ===`);
      
      // Navigate to page
      await page.goto(`http://localhost:5173${pageInfo.url}`);
      
      // Wait for page to fully load and animations to complete
      await page.waitForLoadState('networkidle');
      await page.waitForTimeout(3000); // Extra time for GSAP animations
      
      // Take full-page screenshot
      const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
      await page.screenshot({ 
        path: `test-results/mobile-${pageInfo.name.toLowerCase()}-${timestamp}.png`,
        fullPage: true 
      });
      
      console.log(`✓ Screenshot captured for ${pageInfo.name} page`);
      
      // Test navigation elements
      const navElements = await page.locator('nav').count();
      console.log(`Navigation elements found: ${navElements}`);
      
      // Test for mobile-specific issues
      await testMobileIssues(page, pageInfo.name);
      
      // Test touch targets
      await testTouchTargets(page, pageInfo.name);
      
      // Test card layouts if present
      await testCardLayouts(page, pageInfo.name);
      
      // Test horizontal scrolling
      await testHorizontalScrolling(page, pageInfo.name);
    });
  }

  async function testMobileIssues(page: any, pageName: string) {
    console.log(`\n--- Mobile Issues Analysis for ${pageName} ---`);
    
    // Check for elements that might overflow
    const overflowElements = await page.evaluate(() => {
      const elements = Array.from(document.querySelectorAll('*'));
      return elements.filter(el => {
        const rect = el.getBoundingClientRect();
        return rect.width > window.innerWidth;
      }).length;
    });
    
    if (overflowElements > 0) {
      console.log(`⚠️  Found ${overflowElements} elements that may overflow viewport`);
    }
    
    // Check for proper spacing
    const tightSpacing = await page.evaluate(() => {
      const buttons = Array.from(document.querySelectorAll('button, a[role="button"]'));
      let tightCount = 0;
      
      for (let i = 0; i < buttons.length - 1; i++) {
        const current = buttons[i].getBoundingClientRect();
        const next = buttons[i + 1].getBoundingClientRect();
        
        if (Math.abs(current.bottom - next.top) < 8) {
          tightCount++;
        }
      }
      
      return tightCount;
    });
    
    if (tightSpacing > 0) {
      console.log(`⚠️  Found ${tightSpacing} button pairs with insufficient spacing (<8px)`);
    }
  }

  async function testTouchTargets(page: any, pageName: string) {
    console.log(`\n--- Touch Target Analysis for ${pageName} ---`);
    
    const smallTargets = await page.evaluate(() => {
      const interactiveElements = Array.from(document.querySelectorAll('button, a, input, [role="button"]'));
      return interactiveElements.filter(el => {
        const rect = el.getBoundingClientRect();
        return rect.width < 44 || rect.height < 44; // 44px minimum for accessibility
      }).length;
    });
    
    if (smallTargets > 0) {
      console.log(`⚠️  Found ${smallTargets} touch targets smaller than 44px (2025 standard)`);
    } else {
      console.log(`✓ All touch targets meet 44px minimum size requirement`);
    }
  }

  async function testCardLayouts(page: any, pageName: string) {
    console.log(`\n--- Card Layout Analysis for ${pageName} ---`);
    
    const cardContainers = await page.locator('[class*="card"], [class*="grid"]').count();
    
    if (cardContainers > 0) {
      console.log(`Found ${cardContainers} potential card containers`);
      
      // Check if cards are properly responsive
      const cardIssues = await page.evaluate(() => {
        const cards = Array.from(document.querySelectorAll('[class*="card"], .grid > div'));
        let issues = [];
        
        cards.forEach((card, index) => {
          const rect = card.getBoundingClientRect();
          
          // Check if card is too wide for mobile
          if (rect.width > window.innerWidth - 32) { // 16px margin on each side
            issues.push(`Card ${index + 1}: Too wide for mobile (${Math.round(rect.width)}px)`);
          }
          
          // Check if card is visible
          if (rect.width === 0 || rect.height === 0) {
            issues.push(`Card ${index + 1}: Not visible (0 dimensions)`);
          }
        });
        
        return issues;
      });
      
      if (cardIssues.length > 0) {
        console.log(`⚠️  Card layout issues found:`);
        cardIssues.forEach(issue => console.log(`   - ${issue}`));
      } else {
        console.log(`✓ Card layouts appear mobile-friendly`);
      }
    }
  }

  async function testHorizontalScrolling(page: any, pageName: string) {
    console.log(`\n--- Horizontal Scrolling Analysis for ${pageName} ---`);
    
    const hasHorizontalScroll = await page.evaluate(() => {
      return document.documentElement.scrollWidth > document.documentElement.clientWidth;
    });
    
    if (hasHorizontalScroll) {
      console.log(`⚠️  Page has horizontal scrolling (may indicate layout issues)`);
    } else {
      console.log(`✓ No unwanted horizontal scrolling detected`);
    }
    
    // Check for intentional horizontal scroll containers (carousels)
    const carouselContainers = await page.locator('[class*="overflow-x"], [class*="scroll"]').count();
    if (carouselContainers > 0) {
      console.log(`Found ${carouselContainers} intentional horizontal scroll containers`);
    }
  }

  test('Navigation Functionality Test', async ({ page }) => {
    await page.setViewportSize({ width: 375, height: 812 });
    await page.goto('http://localhost:5173/');
    await page.waitForLoadState('networkidle');
    
    console.log('\n=== TESTING NAVIGATION FUNCTIONALITY ===');
    
    // Test all navigation links
    const navLinks = await page.locator('nav a, [role="navigation"] a').all();
    
    for (let i = 0; i < navLinks.length; i++) {
      const link = navLinks[i];
      const href = await link.getAttribute('href');
      const text = await link.textContent();
      
      if (href && href.startsWith('/')) {
        try {
          await link.click();
          await page.waitForLoadState('networkidle');
          await page.waitForTimeout(1000);
          
          const currentUrl = page.url();
          console.log(`✓ Navigation to "${text}" (${href}) successful - Current URL: ${currentUrl}`);
          
        } catch (error) {
          console.log(`⚠️  Navigation to "${text}" (${href}) failed: ${error}`);
        }
      }
    }
  });
});
