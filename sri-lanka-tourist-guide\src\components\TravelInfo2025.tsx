import React, { useEffect, useRef } from 'react'
import { motion } from 'framer-motion'
import { gsap } from 'gsap'
import { ScrollTrigger } from 'gsap/ScrollTrigger'

gsap.registerPlugin(ScrollTrigger)

interface TravelInfoSection {
  id: string
  title: string
  icon: string
  content: {
    overview: string
    details: string[]
    tips: string[]
    important?: string[]
  }
}

const travelInfo2025: TravelInfoSection[] = [
  {
    id: 'visa-requirements',
    title: 'Visa Requirements 2025',
    icon: '📋',
    content: {
      overview: 'Electronic Travel Authorization (ETA) required for most visitors, valid for 30 days.',
      details: [
        'Apply online for ETA at eta.gov.lk before travel',
        'Passport must be valid for at least 6 months beyond departure date',
        'Return/onward ticket and proof of sufficient funds required',
        'ETA allows single or double entry for tourism purposes',
        'Extension possible at Department of Immigration in Colombo'
      ],
      tips: [
        'Apply for ETA at least 3 days before travel',
        'Print ETA approval and carry during travel',
        'Complete online arrival card 3 days before arrival',
        'Keep digital and physical copies of all documents'
      ],
      important: [
        'Visa status cannot be converted once in Sri Lanka',
        'Journalists need special permissions for northern districts',
        'Entry into Sri Lankan waters requires prior permission'
      ]
    }
  },
  {
    id: 'weather-seasons',
    title: 'Weather & Best Time to Visit 2025',
    icon: '🌤️',
    content: {
      overview: 'Sri Lanka has two monsoon seasons affecting different regions at different times.',
      details: [
        'Southwest Monsoon: May to September (affects west and south coasts)',
        'Northeast Monsoon: October to January (affects north and east coasts)',
        'Inter-monsoon periods: March-April and October-November',
        'Hill country: Cooler temperatures year-round (15-20°C)',
        'Coastal areas: Warm temperatures (26-30°C) with high humidity'
      ],
      tips: [
        'December to March: Best for west and south coasts',
        'April to September: Ideal for east coast and cultural triangle',
        'Year-round: Hill country destinations like Kandy and Ella',
        'Avoid travel during heavy monsoon periods in affected regions'
      ],
      important: [
        'Climate change is increasing extreme weather events',
        'Monitor weather forecasts and local advisories',
        'Flooding and landslides possible during monsoon seasons'
      ]
    }
  },
  {
    id: 'health-safety',
    title: 'Health & Safety 2025',
    icon: '🏥',
    content: {
      overview: 'Generally safe destination with good healthcare in major cities, some health precautions needed.',
      details: [
        'Routine vaccinations: Ensure MMR, DPT, flu, and COVID-19 are up to date',
        'Recommended vaccines: Hepatitis A & B, Typhoid, Japanese Encephalitis',
        'Malaria risk: Low, mainly in rural areas during monsoon',
        'Dengue fever: Year-round risk, especially during rainy seasons',
        'Medical facilities: Good in Colombo, limited in remote areas'
      ],
      tips: [
        'Use insect repellent and protective clothing',
        'Drink bottled or boiled water, avoid ice in drinks',
        'Eat at reputable restaurants, avoid street food initially',
        'Get comprehensive travel insurance including medical evacuation'
      ],
      important: [
        'Medical evacuation may be necessary for serious conditions',
        'Rabies risk from stray dogs - avoid contact',
        'Some medicines may have limited availability'
      ]
    }
  },
  {
    id: 'currency-costs',
    title: 'Currency & Costs 2025',
    icon: '💰',
    content: {
      overview: 'Sri Lankan Rupee (LKR) is the local currency. Budget-friendly destination with varying costs.',
      details: [
        'Currency: Sri Lankan Rupee (LKR), approximately 300-320 LKR = 1 USD',
        'Budget travel: $25-40 per day (guesthouses, local food, public transport)',
        'Mid-range travel: $50-100 per day (hotels, restaurants, private transport)',
        'Luxury travel: $150+ per day (resorts, fine dining, private tours)',
        'ATMs available in major cities, some accept international cards'
      ],
      tips: [
        'Carry cash for rural areas and small vendors',
        'Credit cards accepted in major hotels and restaurants',
        'Negotiate prices for tuk-tuks and souvenirs',
        'Tipping: 10% in restaurants, round up for services'
      ],
      important: [
        'Economic situation may affect availability of some goods',
        'Power outages possible but infrequent',
        'Confirm payment methods before making purchases'
      ]
    }
  },
  {
    id: 'transportation',
    title: 'Transportation 2025',
    icon: '🚗',
    content: {
      overview: 'Various transport options available, from trains to tuk-tuks, with varying comfort levels.',
      details: [
        'Trains: Scenic routes, especially Kandy to Ella, book in advance',
        'Buses: Extensive network, can be crowded, budget-friendly',
        'Tuk-tuks: Convenient for short distances, negotiate fare beforehand',
        'Private cars/drivers: Most comfortable, arrange through hotels',
        'Domestic flights: Limited routes, mainly to Jaffna'
      ],
      tips: [
        'Book train tickets online or at stations early morning',
        'Use ride-hailing apps like PickMe in major cities',
        'Carry small bills for public transport',
        'International Driving Permit required for self-driving'
      ],
      important: [
        'Road conditions poor outside major cities',
        'Traffic drives on the left side',
        'Military checkpoints possible, carry ID always'
      ]
    }
  },
  {
    id: 'cultural-etiquette',
    title: 'Cultural Etiquette 2025',
    icon: '🙏',
    content: {
      overview: 'Respectful behavior essential when visiting temples and interacting with locals.',
      details: [
        'Temple visits: Remove shoes and hats, dress modestly (cover shoulders/knees)',
        'Buddha statues: Never pose for photos with back to Buddha, no pointing',
        'Greetings: "Ayubowan" with palms together, handshakes common in business',
        'Dress code: Conservative clothing, especially in religious sites',
        'Photography: Ask permission before photographing people'
      ],
      tips: [
        'Learn basic Sinhala/Tamil phrases for better interactions',
        'Respect religious customs and ceremonies',
        'Remove Buddha-themed tattoos/jewelry before visiting',
        'Use right hand for giving/receiving items'
      ],
      important: [
        'Posing with Buddha statues is illegal and punishable',
        'Alcohol consumption prohibited in public places',
        'LGBTQ+ travelers should exercise caution'
      ]
    }
  }
]

const TravelInfo2025: React.FC = () => {
  const containerRef = useRef<HTMLDivElement>(null)
  const itemRefs = useRef<(HTMLDivElement | null)[]>([])

  useEffect(() => {
    if (!containerRef.current) return

    const items = itemRefs.current.filter(Boolean)

    // Set initial states
    gsap.set(items, { opacity: 0, y: 50, scale: 0.95 })

    // Animate each section
    items.forEach((item, index) => {
      if (!item) return

      ScrollTrigger.create({
        trigger: item,
        start: 'top 85%',
        end: 'bottom 15%',
        onEnter: () => {
          gsap.to(item, {
            opacity: 1,
            y: 0,
            scale: 1,
            duration: 0.8,
            ease: 'power2.out',
            delay: index * 0.1
          })
        },
        onLeave: () => {
          gsap.to(item, {
            opacity: 0.7,
            scale: 0.98,
            duration: 0.3,
            ease: 'power2.out'
          })
        },
        onEnterBack: () => {
          gsap.to(item, {
            opacity: 1,
            scale: 1,
            duration: 0.3,
            ease: 'power2.out'
          })
        },
        onLeaveBack: () => {
          gsap.to(item, {
            opacity: 0,
            y: 50,
            scale: 0.95,
            duration: 0.3,
            ease: 'power2.out'
          })
        }
      })
    })

    // Cleanup ScrollTriggers
    return () => {
      ScrollTrigger.getAll().forEach(trigger => {
        if (items.includes(trigger.trigger as HTMLDivElement)) {
          trigger.kill()
        }
      })
    }
  }, [])

  return (
    <section className="section-padding bg-gradient-warm">
      <div className="container-max">
        <div className="text-center mb-12">
          <motion.h2
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
            className="text-4xl md:text-5xl font-bold text-primary-navy mb-6"
          >
            Essential Travel Information 2025
          </motion.h2>
          <motion.p
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.2 }}
            viewport={{ once: true }}
            className="text-lg md:text-xl text-neutral-gray-600 max-w-3xl mx-auto leading-relaxed"
          >
            Everything you need to know for a safe and enjoyable trip to Sri Lanka
          </motion.p>
        </div>

        <div ref={containerRef} className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          {travelInfo2025.map((section, index) => (
            <div
              key={section.id}
              ref={el => itemRefs.current[index] = el}
              className="travel-info-card"
            >
              <motion.div
                className="bg-neutral-white rounded-3xl shadow-soft border border-neutral-gray-200 overflow-hidden hover:shadow-medium transition-all duration-300 h-full"
                whileHover={{ y: -2, scale: 1.02 }}
              >
                {/* Header - 2025 Accessible Style */}
                <div className="bg-gradient-cool text-primary-navy p-6">
                  <div className="flex items-center space-x-3">
                    <span className="text-3xl filter drop-shadow-sm">{section.icon}</span>
                    <h3 className="text-xl md:text-2xl font-bold text-primary-navy">{section.title}</h3>
                  </div>
                  <p className="mt-3 text-neutral-gray-600 leading-relaxed font-medium">
                    {section.content.overview}
                  </p>
                </div>

                {/* Content */}
                <div className="p-6 space-y-6">
                  {/* Details - 2025 Accessible Style */}
                  <div className="bg-bg-pastel-teal p-4 rounded-2xl border border-primary-teal/20">
                    <h4 className="text-lg font-semibold text-primary-teal mb-3 flex items-center">
                      <span className="w-2 h-2 bg-primary-teal rounded-full mr-2"></span>
                      Key Information
                    </h4>
                    <ul className="space-y-2">
                      {section.content.details.map((detail, idx) => (
                        <li key={idx} className="text-neutral-gray-600 text-sm flex items-start">
                          <span className="w-1.5 h-1.5 bg-primary-teal rounded-full mt-2 mr-3 flex-shrink-0"></span>
                          <span className="leading-relaxed">{detail}</span>
                        </li>
                      ))}
                    </ul>
                  </div>

                  {/* Tips - 2025 Accessible Style */}
                  <div className="bg-bg-pastel-amber p-4 rounded-2xl border border-secondary-amber/20">
                    <h4 className="text-lg font-semibold text-secondary-amber mb-3 flex items-center">
                      <span className="w-2 h-2 bg-secondary-amber rounded-full mr-2"></span>
                      Practical Tips
                    </h4>
                    <ul className="space-y-2">
                      {section.content.tips.map((tip, idx) => (
                        <li key={idx} className="text-neutral-gray-600 text-sm flex items-start">
                          <span className="w-1.5 h-1.5 bg-secondary-amber rounded-full mt-2 mr-3 flex-shrink-0"></span>
                          <span className="leading-relaxed">{tip}</span>
                        </li>
                      ))}
                    </ul>
                  </div>

                  {/* Important Notes - 2025 Accessible Style */}
                  {section.content.important && (
                    <div className="bg-bg-pastel-coral border border-primary-coral/20 rounded-2xl p-4">
                      <h4 className="text-lg font-semibold text-primary-coral mb-3 flex items-center">
                        <span className="w-2 h-2 bg-primary-coral rounded-full mr-2"></span>
                        Important Notes
                      </h4>
                      <ul className="space-y-2">
                        {section.content.important.map((note, idx) => (
                          <li key={idx} className="text-neutral-gray-600 text-sm flex items-start">
                            <span className="w-1.5 h-1.5 bg-primary-coral rounded-full mt-2 mr-3 flex-shrink-0"></span>
                            <span className="leading-relaxed font-medium">{note}</span>
                          </li>
                        ))}
                      </ul>
                    </div>
                  )}
                </div>
              </motion.div>
            </div>
          ))}
        </div>

        {/* Emergency Contacts */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
          className="mt-12 bg-red-50 border border-red-200 rounded-xl p-6"
        >
          <h3 className="text-xl font-bold text-red-800 mb-4 flex items-center">
            <span className="text-2xl mr-3">🚨</span>
            Emergency Contacts
          </h3>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 text-sm">
            <div className="text-red-700">
              <strong>Police:</strong> 119 / 118
            </div>
            <div className="text-red-700">
              <strong>Tourist Police:</strong> +94 11 242 1052
            </div>
            <div className="text-red-700">
              <strong>Medical Emergency:</strong> 110
            </div>
            <div className="text-red-700">
              <strong>Fire Department:</strong> +94 11 242 2222
            </div>
          </div>
        </motion.div>
      </div>
    </section>
  )
}

export default TravelInfo2025
