import { test, expect } from '@playwright/test';

test.describe('Systematic Page Analysis - 2025 Best Practices', () => {
  test('Activities Page - Desktop and Mobile Screenshots', async ({ page }) => {
    console.log('📸 Taking Activities page screenshots for analysis...');
    
    // Desktop screenshot
    await page.setViewportSize({ width: 1200, height: 800 });
    await page.goto('http://localhost:5174/activities');
    await page.waitForLoadState('networkidle');
    await page.waitForTimeout(2000); // Wait for any animations
    
    await page.screenshot({ 
      path: 'activities-desktop-analysis.png', 
      fullPage: true 
    });
    console.log('✅ Desktop screenshot saved');
    
    // Mobile screenshot
    await page.setViewportSize({ width: 375, height: 667 });
    await page.reload();
    await page.waitForLoadState('networkidle');
    await page.waitForTimeout(2000);
    
    await page.screenshot({ 
      path: 'activities-mobile-analysis.png', 
      fullPage: true 
    });
    console.log('✅ Mobile screenshot saved');
    
    // Analyze mobile card display issues
    const mobileCards = page.locator('.md\\:hidden .flex-shrink-0');
    const mobileCardCount = await mobileCards.count();
    console.log(`📱 Mobile cards found: ${mobileCardCount}`);
    
    if (mobileCardCount > 0) {
      // Check if cards are properly visible
      for (let i = 0; i < Math.min(mobileCardCount, 3); i++) {
        const card = mobileCards.nth(i);
        const isVisible = await card.isVisible();
        const bounds = await card.boundingBox();
        
        console.log(`Card ${i + 1}: visible=${isVisible}, bounds=${JSON.stringify(bounds)}`);
      }
    }
    
    // Check for GSAP animations that might be disrupting UX
    const animatedElements = page.locator('.gsap-fade-in');
    const animatedCount = await animatedElements.count();
    console.log(`🎬 Elements with GSAP animations: ${animatedCount}`);
  });

  test('Destinations Page - Desktop and Mobile Screenshots', async ({ page }) => {
    console.log('📸 Taking Destinations page screenshots for analysis...');
    
    // Desktop screenshot
    await page.setViewportSize({ width: 1200, height: 800 });
    await page.goto('http://localhost:5174/destinations');
    await page.waitForLoadState('networkidle');
    await page.waitForTimeout(2000);
    
    await page.screenshot({ 
      path: 'destinations-desktop-analysis.png', 
      fullPage: true 
    });
    console.log('✅ Desktop screenshot saved');
    
    // Mobile screenshot
    await page.setViewportSize({ width: 375, height: 667 });
    await page.reload();
    await page.waitForLoadState('networkidle');
    await page.waitForTimeout(2000);
    
    await page.screenshot({ 
      path: 'destinations-mobile-analysis.png', 
      fullPage: true 
    });
    console.log('✅ Mobile screenshot saved');
  });

  test('Festivals Page - Desktop and Mobile Screenshots', async ({ page }) => {
    console.log('📸 Taking Festivals page screenshots for analysis...');
    
    // Desktop screenshot
    await page.setViewportSize({ width: 1200, height: 800 });
    await page.goto('http://localhost:5174/festivals');
    await page.waitForLoadState('networkidle');
    await page.waitForTimeout(2000);
    
    await page.screenshot({ 
      path: 'festivals-desktop-analysis.png', 
      fullPage: true 
    });
    console.log('✅ Desktop screenshot saved');
    
    // Mobile screenshot
    await page.setViewportSize({ width: 375, height: 667 });
    await page.reload();
    await page.waitForLoadState('networkidle');
    await page.waitForTimeout(2000);
    
    await page.screenshot({ 
      path: 'festivals-mobile-analysis.png', 
      fullPage: true 
    });
    console.log('✅ Mobile screenshot saved');
    
    // Analyze festival card content issues
    const festivalCards = page.locator('.bg-white\\/90, .festival-card');
    const cardCount = await festivalCards.count();
    console.log(`🎭 Festival cards found: ${cardCount}`);
    
    if (cardCount > 0) {
      for (let i = 0; i < Math.min(cardCount, 5); i++) {
        const card = festivalCards.nth(i);
        const description = card.locator('p').first();
        
        if (await description.count() > 0) {
          const text = await description.textContent();
          const hasEllipsis = text?.includes('...');
          const textLength = text?.length || 0;
          
          console.log(`Festival card ${i + 1}: length=${textLength}, hasEllipsis=${hasEllipsis}, text="${text?.substring(0, 50)}..."`);
        }
      }
    }
  });

  test('Articles Page - Desktop and Mobile Screenshots', async ({ page }) => {
    console.log('📸 Taking Articles page screenshots for analysis...');
    
    // Desktop screenshot
    await page.setViewportSize({ width: 1200, height: 800 });
    await page.goto('http://localhost:5174/articles');
    await page.waitForLoadState('networkidle');
    await page.waitForTimeout(2000);
    
    await page.screenshot({ 
      path: 'articles-desktop-analysis.png', 
      fullPage: true 
    });
    console.log('✅ Desktop screenshot saved');
    
    // Mobile screenshot
    await page.setViewportSize({ width: 375, height: 667 });
    await page.reload();
    await page.waitForLoadState('networkidle');
    await page.waitForTimeout(2000);
    
    await page.screenshot({ 
      path: 'articles-mobile-analysis.png', 
      fullPage: true 
    });
    console.log('✅ Mobile screenshot saved');
  });

  test('Interactive Elements Testing', async ({ page }) => {
    console.log('🔗 Testing all interactive elements...');
    
    await page.setViewportSize({ width: 1200, height: 800 });
    await page.goto('http://localhost:5174');
    await page.waitForLoadState('networkidle');
    
    // Test navigation links
    const navLinks = page.locator('nav a, header a');
    const navCount = await navLinks.count();
    console.log(`🧭 Navigation links found: ${navCount}`);
    
    for (let i = 0; i < navCount; i++) {
      const link = navLinks.nth(i);
      const href = await link.getAttribute('href');
      const text = await link.textContent();
      
      if (href && !href.startsWith('#') && !href.startsWith('mailto:')) {
        console.log(`Testing link: "${text}" -> ${href}`);
        
        try {
          await link.click();
          await page.waitForLoadState('networkidle');
          
          // Take screenshot of the page
          const pageName = href.replace('/', '').replace('#', '') || 'home';
          await page.screenshot({ 
            path: `interactive-test-${pageName}.png`, 
            fullPage: false 
          });
          
          // Go back to home for next test
          await page.goto('http://localhost:5174');
          await page.waitForLoadState('networkidle');
        } catch (error) {
          console.error(`❌ Error testing link "${text}": ${error}`);
        }
      }
    }
  });
});
