import React, { useEffect, useState } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { ArrowUpIcon } from '@heroicons/react/24/outline'

const FloatingActionButton: React.FC = () => {
  const [isVisible, setIsVisible] = useState(false)

  useEffect(() => {
    // Show/hide FAB based on scroll position with throttling
    let ticking = false

    const showFab = () => {
      if (!ticking) {
        requestAnimationFrame(() => {
          setIsVisible(window.scrollY > 400)
          ticking = false
        })
        ticking = true
      }
    }

    window.addEventListener('scroll', showFab, { passive: true })
    showFab() // Check initial position

    return () => window.removeEventListener('scroll', showFab)
  }, [])

  const scrollToTop = () => {
    window.scrollTo({
      top: 0,
      behavior: 'smooth'
    })
  }

  // Modern 2025 FAB variants - Clean and minimal
  const fabVariants = {
    hidden: {
      scale: 0,
      opacity: 0,
      y: 20
    },
    visible: {
      scale: 1,
      opacity: 1,
      y: 0,
      transition: {
        type: 'spring' as const,
        stiffness: 400,
        damping: 25,
        duration: 0.4
      }
    },
    exit: {
      scale: 0,
      opacity: 0,
      y: 20,
      transition: {
        duration: 0.2
      }
    }
  }

  return (
    <AnimatePresence mode="wait">
      {isVisible && (
        <motion.button
          variants={fabVariants}
          initial="hidden"
          animate="visible"
          exit="exit"
          onClick={scrollToTop}
          className="fixed bottom-6 right-6 z-50 group"
          whileHover={{ scale: 1.05 }}
          whileTap={{ scale: 0.95 }}
          aria-label="Scroll to top"
        >
          {/* Modern 2025 FAB Design */}
          <div className="relative">
            {/* Main Button */}
            <div className="w-14 h-14 bg-white/90 backdrop-blur-md border border-gray-200/50 rounded-2xl shadow-medium hover:shadow-strong transition-all duration-300 flex items-center justify-center group-hover:bg-white group-hover:border-primary-200">
              <ArrowUpIcon className="h-6 w-6 text-gray-700 group-hover:text-primary-600 transition-colors duration-300" />
            </div>

            {/* Subtle Glow Effect */}
            <div className="absolute inset-0 bg-gradient-to-r from-primary-500/20 to-accent-500/20 rounded-2xl opacity-0 group-hover:opacity-100 transition-opacity duration-300 -z-10 blur-sm"></div>

            {/* Progress Ring - Optional */}
            <div className="absolute inset-0 rounded-2xl border-2 border-transparent bg-gradient-to-r from-primary-500 to-accent-500 opacity-0 group-hover:opacity-20 transition-opacity duration-300 -z-20"></div>
          </div>

          {/* Tooltip */}
          <div className="absolute bottom-full right-0 mb-2 px-3 py-1.5 bg-gray-900 text-white text-sm rounded-lg opacity-0 group-hover:opacity-100 transition-opacity duration-300 whitespace-nowrap pointer-events-none">
            Back to top
            <div className="absolute top-full right-4 w-0 h-0 border-l-4 border-r-4 border-t-4 border-transparent border-t-gray-900"></div>
          </div>
        </motion.button>
      )}
    </AnimatePresence>
  )
}

export default FloatingActionButton
