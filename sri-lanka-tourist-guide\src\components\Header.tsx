import React, { useState, useEffect, useRef } from 'react'
import { Link, useLocation } from 'react-router-dom'
import { motion } from 'framer-motion'
import { Bars3Icon, XMarkIcon } from '@heroicons/react/24/outline'
import { useKeyboardNavigation } from '../hooks/useAccessibility'

const Header: React.FC = () => {
  const [isMenuOpen, setIsMenuOpen] = useState(false)
  const [currentFocusIndex, setCurrentFocusIndex] = useState(-1)
  const location = useLocation()
  const mobileMenuRef = useRef<HTMLDivElement>(null)
  const mobileButtonRef = useRef<HTMLButtonElement>(null)
  // const { trapFocus, restoreFocus } = useFocusManagement()
  const { handleKeyboardNavigation } = useKeyboardNavigation()

  const navigation = [
    { name: 'Home', href: '/' },
    { name: 'Destinations', href: '/destinations' },
    { name: 'Activities', href: '/activities' },
    { name: 'Culture', href: '/culture' },
    { name: 'Festivals', href: '/festivals' },
    { name: 'Articles', href: '/articles' },
    { name: 'Travel Tips', href: '/travel-tips' },
    { name: 'Contact', href: '/contact' },
  ]

  const isActive = (path: string) => location.pathname === path

  // Enhanced accessibility functions
  const toggleMobileMenu = () => {
    setIsMenuOpen(!isMenuOpen)
    setCurrentFocusIndex(-1)
  }

  const closeMobileMenu = () => {
    setIsMenuOpen(false)
    setCurrentFocusIndex(-1)
    // Return focus to menu button
    mobileButtonRef.current?.focus()
  }

  // Handle keyboard navigation in mobile menu
  const handleMobileMenuKeyDown = (e: React.KeyboardEvent) => {
    if (!isMenuOpen) return

    const menuItems = mobileMenuRef.current?.querySelectorAll('a') || []
    const menuItemsArray = Array.from(menuItems) as HTMLElement[]

    switch (e.key) {
      case 'Escape':
        e.preventDefault()
        closeMobileMenu()
        break
      case 'Tab':
        // Allow normal tab behavior but ensure focus stays within menu
        if (e.shiftKey && currentFocusIndex === 0) {
          e.preventDefault()
          mobileButtonRef.current?.focus()
        } else if (!e.shiftKey && currentFocusIndex === menuItemsArray.length - 1) {
          e.preventDefault()
          mobileButtonRef.current?.focus()
        }
        break
      case 'ArrowDown':
      case 'ArrowUp':
        e.preventDefault()
        handleKeyboardNavigation(e, menuItemsArray, currentFocusIndex, setCurrentFocusIndex)
        break
      case 'Home':
        e.preventDefault()
        setCurrentFocusIndex(0)
        menuItemsArray[0]?.focus()
        break
      case 'End':
        e.preventDefault()
        const lastIndex = menuItemsArray.length - 1
        setCurrentFocusIndex(lastIndex)
        menuItemsArray[lastIndex]?.focus()
        break
    }
  }

  // Handle clicks outside mobile menu
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (isMenuOpen &&
          mobileMenuRef.current &&
          !mobileMenuRef.current.contains(event.target as Node) &&
          !mobileButtonRef.current?.contains(event.target as Node)) {
        closeMobileMenu()
      }
    }

    if (isMenuOpen) {
      document.addEventListener('mousedown', handleClickOutside)
      // Focus first menu item when menu opens
      setTimeout(() => {
        const firstMenuItem = mobileMenuRef.current?.querySelector('a') as HTMLElement
        firstMenuItem?.focus()
        setCurrentFocusIndex(0)
      }, 100)
    }

    return () => {
      document.removeEventListener('mousedown', handleClickOutside)
    }
  }, [isMenuOpen])

  return (
    <>
      {/* Skip Link for Screen Readers */}
      <a
        href="#main-content"
        className="sr-only focus:not-sr-only focus:absolute focus:top-4 focus:left-2 focus:right-2 sm:focus:left-4 sm:focus:right-auto focus:z-50 focus:px-2 sm:focus:px-4 focus:py-2 focus:bg-primary-600 focus:text-white focus:rounded-md focus:shadow-medium focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2 focus:text-sm"
      >
        Skip to main content
      </a>

      <header className="bg-white/90 backdrop-blur-xl shadow-2xl border-b border-gradient-to-r from-transparent via-gray-100/50 to-transparent sticky top-0 z-50" role="banner" style={{
        background: 'linear-gradient(135deg, rgba(255,255,255,0.95) 0%, rgba(248,250,252,0.95) 100%)',
        boxShadow: 'var(--shadow-soft)'
      }}>
      <nav className="container-max">
        <div className="flex justify-between items-center py-4">
          {/* Logo - 2025 WCAG Compliant */}
          <Link to="/" className="flex items-center space-x-2 min-h-[44px] py-2 group">
            <motion.div
              whileHover={{ scale: 1.05, rotate: 1 }}
              whileTap={{ scale: 0.95 }}
              className="text-2xl font-bold text-primary-navy group-hover:text-primary-coral transition-all duration-300"
              style={{
                filter: 'drop-shadow(0 2px 4px rgba(0,0,0,0.1))'
              }}
            >
              🇱🇰 Sri Lanka Guide
            </motion.div>
          </Link>

          {/* Desktop Navigation - 2025 Enhanced with Proper Color Contrast */}
          <nav className="hidden md:flex space-x-2 lg:space-x-4" role="navigation" aria-label="Main navigation">
            {navigation.map((item) => (
              <Link
                key={item.name}
                to={item.href}
                className={`relative px-4 py-3 text-sm font-semibold transition-all duration-300 focus:outline-none focus:ring-2 focus:ring-primary-coral focus:ring-offset-2 rounded-2xl group min-h-[48px] flex items-center justify-center ${
                  isActive(item.href)
                    ? 'text-white bg-primary-navy shadow-medium border-2 border-primary-navy'
                    : 'text-primary-navy hover:text-white hover:bg-primary-coral hover:shadow-soft border-2 border-transparent hover:border-primary-coral'
                }`}
                aria-current={isActive(item.href) ? 'page' : undefined}
                tabIndex={0}
              >
                <span className="relative z-10">{item.name}</span>
                {isActive(item.href) && (
                  <motion.div
                    layoutId="activeTab"
                    className="absolute inset-0 bg-primary-navy rounded-2xl shadow-medium"
                    initial={false}
                    transition={{ type: "spring", stiffness: 500, damping: 30 }}
                    aria-hidden="true"
                  />
                )}
              </Link>
            ))}
          </nav>

          {/* Mobile menu button */}
          <div className="md:hidden">
            <button
              ref={mobileButtonRef}
              type="button"
              onClick={toggleMobileMenu}
              onKeyDown={(e) => {
                if (e.key === 'Enter' || e.key === ' ') {
                  e.preventDefault()
                  toggleMobileMenu()
                }
              }}
              className="p-3 rounded-md text-primary-navy hover:text-white hover:bg-primary-coral mobile-menu-button focus:outline-none focus:ring-2 focus:ring-primary-coral focus:ring-offset-2 min-h-[44px] min-w-[44px] flex items-center justify-center"
              aria-label={isMenuOpen ? "Close navigation menu" : "Open navigation menu"}
              aria-expanded={isMenuOpen ? "true" : "false"}
              aria-controls="mobile-menu"
              aria-haspopup="true"
            >
              <span className="sr-only">
                {isMenuOpen ? "Close navigation menu" : "Open navigation menu"}
              </span>
              {isMenuOpen ? (
                <XMarkIcon className="h-6 w-6" aria-hidden="true" />
              ) : (
                <Bars3Icon className="h-6 w-6" aria-hidden="true" />
              )}
            </button>
          </div>
        </div>

        {/* Mobile Navigation */}
        <motion.div
          ref={mobileMenuRef}
          id="mobile-menu"
          initial={{ opacity: 0, height: 0 }}
          animate={isMenuOpen ? { opacity: 1, height: 'auto' } : { opacity: 0, height: 0 }}
          className="mobile-menu md:hidden border-t border-gray-200 overflow-hidden"
          data-testid="mobile-menu"
          role="navigation"
          aria-label="Mobile navigation"
          aria-hidden={!isMenuOpen}
          onKeyDown={handleMobileMenuKeyDown}
          style={{ display: isMenuOpen ? 'block' : 'none' }}
        >
          <ul className="py-4 space-y-3">
            {navigation.map((item, index) => (
              <li key={item.name}>
                <Link
                  to={item.href}
                  onClick={closeMobileMenu}
                  onFocus={() => setCurrentFocusIndex(index)}
                  className={`block px-4 py-4 text-base font-medium rounded-md transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-primary-coral focus:ring-offset-2 focus:ring-offset-white min-h-[56px] flex items-center ${
                    isActive(item.href)
                      ? 'text-white bg-primary-navy border border-primary-navy/20'
                      : 'text-primary-navy hover:text-white hover:bg-primary-coral'
                  }`}
                  aria-current={isActive(item.href) ? 'page' : undefined}
                  tabIndex={isMenuOpen ? 0 : -1}
                >
                  {item.name}
                </Link>
              </li>
            ))}
          </ul>
        </motion.div>
      </nav>
    </header>
    </>
  )
}

export default Header
