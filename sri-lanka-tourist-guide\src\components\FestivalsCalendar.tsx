import React, { useEffect, useRef } from 'react'
import { motion } from 'framer-motion'
import { gsap } from 'gsap'
import { ScrollTrigger } from 'gsap/ScrollTrigger'
import { Festival } from '@/data/culture'
import MobileHorizontalScroll from './MobileHorizontalScroll'

gsap.registerPlugin(ScrollTrigger)

interface FestivalsCalendarProps {
  festivals: Festival[]
  className?: string
}

const FestivalsCalendar: React.FC<FestivalsCalendarProps> = ({ festivals, className = '' }) => {
  const calendarRef = useRef<HTMLDivElement>(null)
  const itemRefs = useRef<(HTMLDivElement | null)[]>([])

  useEffect(() => {
    if (!calendarRef.current) return

    const items = itemRefs.current.filter(Boolean)

    // Set initial states
    gsap.set(items, { opacity: 0, y: 30, scale: 0.95 })

    // Animate each festival card
    items.forEach((item, index) => {
      if (!item) return

      ScrollTrigger.create({
        trigger: item,
        start: 'top 85%',
        end: 'bottom 15%',
        onEnter: () => {
          gsap.to(item, {
            opacity: 1,
            y: 0,
            scale: 1,
            duration: 0.6,
            ease: 'power2.out',
            delay: index * 0.1
          })
        },
        onLeave: () => {
          gsap.to(item, {
            opacity: 0.8,
            scale: 0.98,
            duration: 0.3,
            ease: 'power2.out'
          })
        },
        onEnterBack: () => {
          gsap.to(item, {
            opacity: 1,
            scale: 1,
            duration: 0.3,
            ease: 'power2.out'
          })
        },
        onLeaveBack: () => {
          gsap.to(item, {
            opacity: 0,
            y: 30,
            scale: 0.95,
            duration: 0.3,
            ease: 'power2.out'
          })
        }
      })

      // Add hover animations - 2025 blur-free design
      const handleMouseEnter = () => {
        gsap.to(item, {
          y: -8,
          duration: 0.3,
          ease: 'power2.out',
          boxShadow: '0 20px 40px rgba(0, 0, 0, 0.15)'
        })
      }

      const handleMouseLeave = () => {
        gsap.to(item, {
          y: 0,
          duration: 0.3,
          ease: 'power2.out',
          boxShadow: '0 10px 25px rgba(0, 0, 0, 0.1)'
        })
      }

      item.addEventListener('mouseenter', handleMouseEnter)
      item.addEventListener('mouseleave', handleMouseLeave)

      return () => {
        item.removeEventListener('mouseenter', handleMouseEnter)
        item.removeEventListener('mouseleave', handleMouseLeave)
      }
    })

    // Cleanup ScrollTriggers
    return () => {
      ScrollTrigger.getAll().forEach(trigger => {
        if (items.includes(trigger.trigger as HTMLDivElement)) {
          trigger.kill()
        }
      })
    }
  }, [festivals])

  const getTypeColor = (type: Festival['type']) => {
    switch (type) {
      case 'Buddhist': return 'from-orange-500 to-amber-600'
      case 'Hindu': return 'from-purple-500 to-violet-600'
      case 'Cultural': return 'from-primary-500 to-primary-600'
      case 'National': return 'from-accent-500 to-accent-600'
      case 'Literary': return 'from-secondary-500 to-secondary-600'
      case 'Harvest': return 'from-yellow-500 to-orange-600'
      default: return 'from-gray-500 to-gray-600'
    }
  }

  const getTypeIcon = (type: Festival['type']) => {
    switch (type) {
      case 'Buddhist': return '☸️'
      case 'Hindu': return '🕉️'
      case 'Cultural': return '🎭'
      case 'National': return '🇱🇰'
      case 'Literary': return '📚'
      case 'Harvest': return '🌾'
      default: return '🎉'
    }
  }

  const getCrowdLevelColor = (level: Festival['practicalInfo']['crowdLevel']) => {
    switch (level) {
      case 'Low': return 'bg-success'
      case 'Medium': return 'bg-warning'
      case 'High': return 'bg-orange-100 text-orange-800'
      case 'Very High': return 'bg-error'
      default: return 'bg-gray-100 text-gray-800'
    }
  }

  // Group festivals by month
  const festivalsByMonth = festivals.reduce((acc, festival) => {
    if (!acc[festival.month]) {
      acc[festival.month] = []
    }
    acc[festival.month].push(festival)
    return acc
  }, {} as Record<string, Festival[]>)

  return (
    <div ref={calendarRef} className={`${className}`}>
      <div className="space-y-8 md:space-y-12">
        {Object.entries(festivalsByMonth).map(([month, monthFestivals]) => (
          <div key={month} className="space-y-6">
            {/* Month Header */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6 }}
              className="text-center"
            >
              <h3 className="text-2xl md:text-3xl font-bold text-gray-900 mb-2">
                {month} 2025
              </h3>
              <div className="w-24 h-1 bg-gradient-to-r from-primary-500 to-accent-500 mx-auto rounded-full"></div>
            </motion.div>

            {/* Mobile-First Horizontal Scroll with Desktop Grid */}
            <MobileHorizontalScroll
              itemWidth="w-72"
              gap="gap-4"
              className="festival-cards-container"
            >
              {monthFestivals.map((festival, index) => {
                // Simplified sizing for mobile-first horizontal scroll
                const isHero = index === 0 // First item is always hero
                const isLarge = index % 4 === 1

                return (
                  <div
                    key={festival.id}
                    ref={el => itemRefs.current[festivals.indexOf(festival)] = el}
                    className="festival-card h-full"
                  >
                    <motion.div
                      className={`group relative bg-white/90 backdrop-blur-md rounded-3xl shadow-xl border border-white/30 overflow-hidden hover:shadow-2xl transition-all duration-700 h-full ${isHero ? 'min-h-[400px]' : isLarge ? 'min-h-[300px]' : 'min-h-[200px]'}`}
                      whileHover={{
                        y: -12,
                        boxShadow: '0 25px 50px rgba(0, 0, 0, 0.2)'
                      }}
                      transition={{
                        duration: 0.3,
                        ease: 'easeOut'
                      }}
                    >
                      {/* Modern Glassmorphism Background */}
                      <div className={`absolute inset-0 bg-gradient-to-br ${getTypeColor(festival.type)} opacity-10 group-hover:opacity-20 transition-opacity duration-500`} />
                      <div className="absolute inset-0 bg-gradient-to-t from-black/5 via-transparent to-white/10" />

                      {/* Progressive Blur Edge Effect */}
                      <div className="absolute inset-0 bg-gradient-to-r from-transparent via-transparent to-white/30 opacity-0 group-hover:opacity-100 transition-opacity duration-500" />

                      {/* Festival Type Badge - Modern Design */}
                      <div className="absolute top-3 right-3 z-10">
                        <div className={`inline-flex items-center px-3 py-1.5 rounded-full bg-gradient-to-r ${getTypeColor(festival.type)} text-white text-xs font-semibold shadow-xl backdrop-blur-sm border border-white/20`}>
                          <span className="mr-1.5 text-sm">{getTypeIcon(festival.type)}</span>
                          <span className="hidden sm:inline">{festival.type}</span>
                        </div>
                      </div>

                      <div className={`relative ${isHero ? 'p-6 md:p-8' : isLarge ? 'p-4 md:p-6' : 'p-3 md:p-4'} h-full flex flex-col`}>
                        {/* Enhanced Date with Modern Typography */}
                        <div className="mb-4">
                          <div className={`${isHero ? 'text-4xl md:text-5xl' : isLarge ? 'text-2xl md:text-3xl' : 'text-xl md:text-2xl'} font-black bg-gradient-to-r from-gray-900 via-gray-800 to-gray-900 bg-clip-text text-transparent mb-2 leading-none`}>
                            {festival.date.split(' ')[0]} {/* Day */}
                          </div>
                          <div className={`${isHero ? 'text-sm' : 'text-xs'} text-gray-500 font-semibold flex items-center`}>
                            <span className="w-2 h-2 bg-gradient-to-r from-primary-500 to-accent-500 rounded-full mr-2 animate-pulse"></span>
                            {festival.duration}
                          </div>
                        </div>

                        {/* Enhanced Festival Name with Modern Typography */}
                        <h4 className={`${isHero ? 'text-xl md:text-2xl' : isLarge ? 'text-lg md:text-xl' : 'text-base md:text-lg'} font-bold text-gray-900 mb-3 leading-tight flex-shrink-0 group-hover:text-gradient-luxury transition-all duration-500`}>
                          <span className="text-2xl mr-2">{getTypeIcon(festival.type)}</span>
                          {festival.name}
                        </h4>

                        {/* Enhanced Description with Better Typography */}
                        <p className={`text-gray-600 ${isHero ? 'text-base mb-6' : isLarge ? 'text-sm mb-4' : 'text-xs mb-3'} leading-relaxed flex-grow ${isHero ? 'line-clamp-6' : isLarge ? 'line-clamp-4' : 'line-clamp-2'} group-hover:text-gray-700 transition-colors duration-500`}>
                          {festival.description}
                        </p>

                        {/* Enhanced Location with Modern Design */}
                        <div className="mb-4 flex-shrink-0">
                          <div className={`flex items-center ${isHero ? 'text-sm' : isLarge ? 'text-sm' : 'text-xs'} text-gray-600 bg-gray-50/50 backdrop-blur-sm rounded-xl px-3 py-2 border border-gray-100/50`}>
                            <span className="mr-2 text-lg">📍</span>
                            <span className="truncate font-medium">{festival.location}</span>
                          </div>
                        </div>

                        {/* Ultra-Modern Info Pills with Glassmorphism */}
                        <div className="flex flex-wrap gap-2 mb-4 flex-shrink-0">
                          <span className={`inline-flex items-center px-3 py-1.5 rounded-xl text-xs font-semibold ${getCrowdLevelColor(festival.practicalInfo.crowdLevel)} backdrop-blur-md border border-white/20 shadow-sm`}>
                            <span className="mr-1.5">👥</span>
                            {festival.practicalInfo.crowdLevel}
                          </span>
                          {festival.practicalInfo.bookingRequired && (
                            <span className="inline-flex items-center px-3 py-1.5 bg-blue-100/90 text-blue-700 rounded-xl text-xs font-semibold backdrop-blur-md border border-blue-200/50 shadow-sm">
                              <span className="mr-1.5">🎫</span>
                              Booking Required
                            </span>
                          )}
                        </div>

                        {/* Enhanced Highlights - Show in larger cards with better design */}
                        {(isHero || isLarge) && (
                          <div className="flex-shrink-0">
                            <h5 className={`${isHero ? 'text-sm' : 'text-xs'} font-bold text-gray-900 mb-3 flex items-center`}>
                              <span className="text-lg mr-2">✨</span>
                              Highlights
                            </h5>
                            <ul className="space-y-2">
                              {festival.highlights.slice(0, isHero ? 4 : 2).map((highlight, idx) => (
                                <li key={idx} className={`${isHero ? 'text-sm' : 'text-xs'} text-gray-600 flex items-start group/item`}>
                                  <span className="w-2 h-2 bg-gradient-to-r from-primary-500 to-accent-500 rounded-full mt-1.5 mr-3 flex-shrink-0 group-hover/item:scale-125 transition-transform duration-300"></span>
                                  <span className={`${isHero ? 'line-clamp-2' : 'line-clamp-1'} group-hover/item:text-gray-800 transition-colors duration-300`}>{highlight}</span>
                                </li>
                              ))}
                            </ul>
                          </div>
                        )}

                        {/* Interactive Hover Element - 2025 Trend */}
                        <div className="absolute bottom-4 right-4 opacity-0 group-hover:opacity-100 transition-all duration-500 transform translate-y-2 group-hover:translate-y-0">
                          <div className="w-8 h-8 bg-gradient-to-r from-primary-500 to-accent-500 rounded-full flex items-center justify-center shadow-lg backdrop-blur-sm">
                            <span className="text-white text-sm">→</span>
                          </div>
                        </div>
                      </div>

                      {/* Subtle 3D Hover Effect Border */}
                      <div className="absolute inset-0 rounded-2xl border border-transparent group-hover:border-white/30 transition-all duration-500" />
                    </motion.div>
                  </div>
                )
              })}
            </MobileHorizontalScroll>
          </div>
        ))}
      </div>
    </div>
  )
}

export default FestivalsCalendar
