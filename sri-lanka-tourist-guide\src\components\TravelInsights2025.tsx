import React, { useState } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import {
  ChartBarIcon,
  GlobeAltIcon,
  ArrowTrendingUpIcon,
  UserGroupIcon,
  QuestionMarkCircleIcon,
  ExclamationTriangleIcon,
  HeartIcon,
  LightBulbIcon
} from '@heroicons/react/24/outline'
import { travelTrends2025, touristInsights2025, globalRankings2025, travelStatistics2025 } from '@/data/travelTrends2025'

interface TravelInsights2025Props {
  className?: string
}

const TravelInsights2025: React.FC<TravelInsights2025Props> = ({ className = '' }) => {
  const [activeTab, setActiveTab] = useState<'trends' | 'insights' | 'rankings' | 'statistics'>('trends')

  const tabs = [
    { id: 'trends', label: '2025 Trends', icon: ArrowTrendingUpIcon },
    { id: 'insights', label: 'Tourist Insights', icon: UserGroupIcon },
    { id: 'rankings', label: 'Global Rankings', icon: ChartBarIcon },
    { id: 'statistics', label: 'Travel Stats', icon: GlobeAltIcon }
  ]

  const getInsightIcon = (category: string) => {
    switch (category) {
      case 'common-question':
        return QuestionMarkCircleIcon
      case 'pain-point':
        return ExclamationTriangleIcon
      case 'positive-feedback':
        return HeartIcon
      case 'recommendation':
        return LightBulbIcon
      default:
        return QuestionMarkCircleIcon
    }
  }

  const getInsightColor = (category: string) => {
    switch (category) {
      case 'common-question':
        return 'text-primary-teal bg-bg-pastel-teal border-primary-teal/20'
      case 'pain-point':
        return 'text-primary-coral bg-bg-pastel-coral border-primary-coral/20'
      case 'positive-feedback':
        return 'text-secondary-sage bg-bg-pastel-sage border-secondary-sage/20'
      case 'recommendation':
        return 'text-secondary-lavender bg-bg-pastel-lavender border-secondary-lavender/20'
      default:
        return 'text-neutral-gray-600 bg-neutral-light border-neutral-gray-200'
    }
  }

  return (
    <section className={`py-16 px-4 sm:px-6 lg:px-8 bg-gradient-nature ${className}`}>
      <div className="container-max">
        <div className="text-center mb-12">
          <motion.h2
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            viewport={{ once: true }}
            className="text-4xl md:text-5xl font-bold text-primary-navy mb-6"
          >
            Sri Lanka Travel Insights 2025
          </motion.h2>
          <motion.p
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.2 }}
            viewport={{ once: true }}
            className="text-lg md:text-xl text-neutral-gray-600 max-w-3xl mx-auto leading-relaxed"
          >
            Stay ahead with the latest travel trends, tourist insights, and comprehensive data about Sri Lanka
          </motion.p>
        </div>

        {/* Tab Navigation - 2025 Accessible Style */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.3 }}
          viewport={{ once: true }}
          className="flex flex-wrap justify-center mb-8 bg-neutral-white rounded-3xl p-3 shadow-soft border border-neutral-gray-200"
          role="tablist"
          aria-label="Travel Insights Categories"
        >
          {tabs.map((tab) => {
            const Icon = tab.icon
            return (
              <button
                type="button"
                key={tab.id}
                onClick={() => setActiveTab(tab.id as any)}
                role="tab"
                aria-selected={activeTab === tab.id ? 'true' : 'false'}
                aria-label={`${tab.label} tab`}
                tabIndex={activeTab === tab.id ? 0 : -1}
                className={`flex items-center space-x-2 px-6 py-3 rounded-2xl font-semibold transition-all duration-300 border-2 ${
                  activeTab === tab.id
                    ? 'text-white shadow-lg'
                    : 'text-gray-700 bg-white hover:text-red-500 hover:bg-red-50 border-gray-200'
                }`}
                style={{
                  backgroundColor: activeTab === tab.id ? '#e74c3c' : undefined,
                  borderColor: activeTab === tab.id ? '#c0392b' : undefined
                }}
              >
                <Icon className="h-5 w-5" />
                <span>{tab.label}</span>
              </button>
            )
          })}
        </motion.div>

        {/* Tab Content */}
        <AnimatePresence mode="wait">
          <motion.div
            key={activeTab}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -20 }}
            transition={{ duration: 0.4 }}
          >
            {activeTab === 'trends' && (
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                {travelTrends2025.map((trend) => (
                  <motion.div
                    key={trend.id}
                    className="bg-neutral-white rounded-3xl shadow-soft p-8 hover:shadow-medium transition-all duration-300 border border-neutral-gray-200"
                    whileHover={{ y: -2, scale: 1.02 }}
                  >
                    <h3 className="text-xl font-bold text-primary-navy mb-4">{trend.name}</h3>
                    <p className="text-neutral-gray-600 mb-6 leading-relaxed">{trend.description}</p>
                    <div className="mb-6">
                      <h4 className="font-semibold text-primary-teal mb-3">In Sri Lanka:</h4>
                      <p className="text-sm text-neutral-gray-600 leading-relaxed">{trend.relevanceToSriLanka}</p>
                    </div>
                    <div className="mb-4">
                      <h4 className="font-semibold text-primary-teal mb-3">Examples:</h4>
                      <ul className="text-sm text-neutral-gray-600 space-y-2">
                        {trend.examples.slice(0, 3).map((example, idx) => (
                          <li key={idx} className="flex items-start">
                            <span className="text-primary-coral mr-3 font-bold">•</span>
                            <span className="leading-relaxed">{example}</span>
                          </li>
                        ))}
                      </ul>
                    </div>
                  </motion.div>
                ))}
              </div>
            )}

            {activeTab === 'insights' && (
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                {touristInsights2025.map((insight) => {
                  const Icon = getInsightIcon(insight.category)
                  const colorClass = getInsightColor(insight.category)
                  
                  return (
                    <motion.div
                      key={insight.id}
                      className="bg-neutral-white rounded-3xl shadow-soft p-8 hover:shadow-medium transition-all duration-300 border border-neutral-gray-200"
                      whileHover={{ y: -2, scale: 1.02 }}
                    >
                      <div className="flex items-start space-x-4 mb-6">
                        <div className={`p-3 rounded-2xl border ${colorClass}`}>
                          <Icon className="h-6 w-6" />
                        </div>
                        <div className="flex-1">
                          <div className="flex items-center space-x-3 mb-3">
                            <span className="text-xs font-semibold text-neutral-gray-500 uppercase tracking-wide">
                              {insight.category.replace('-', ' ')}
                            </span>
                            <span className={`text-xs px-3 py-1 rounded-full font-medium ${
                              insight.frequency === 'very-common' ? 'bg-accent-error text-white' :
                              insight.frequency === 'common' ? 'bg-accent-warning text-white' :
                              'bg-accent-success text-white'
                            }`}>
                              {insight.frequency}
                            </span>
                          </div>
                          <h3 className="text-lg font-bold text-primary-navy mb-3">{insight.question}</h3>
                          <p className="text-neutral-gray-600 text-sm leading-relaxed">{insight.answer}</p>
                        </div>
                      </div>
                    </motion.div>
                  )
                })}
              </div>
            )}

            {activeTab === 'rankings' && (
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                {globalRankings2025.map((ranking) => (
                  <motion.div
                    key={ranking.id}
                    className="bg-neutral-white rounded-3xl shadow-soft p-8 hover:shadow-medium transition-all duration-300 border border-neutral-gray-200"
                    whileHover={{ y: -2, scale: 1.02 }}
                  >
                    <div className="text-center mb-6">
                      <div className="text-4xl font-bold text-primary-coral mb-3">{ranking.ranking}</div>
                      <h3 className="text-lg font-bold text-primary-navy">{ranking.category}</h3>
                    </div>
                    <p className="text-neutral-gray-600 text-sm mb-4 leading-relaxed">{ranking.description}</p>
                    <div className="text-xs text-neutral-gray-500 bg-bg-pastel-teal p-3 rounded-xl">
                      <strong className="text-primary-teal">Source:</strong> {ranking.source} ({ranking.year})
                    </div>
                  </motion.div>
                ))}
              </div>
            )}

            {activeTab === 'statistics' && (
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                <motion.div
                  className="bg-neutral-white rounded-3xl shadow-soft p-8 text-center hover:shadow-medium transition-all duration-300 border border-neutral-gray-200"
                  whileHover={{ y: -2, scale: 1.02 }}
                >
                  <div className="text-4xl font-bold text-primary-coral mb-3">{travelStatistics2025.averageStayDuration}</div>
                  <div className="text-neutral-gray-600 font-semibold">Average Stay</div>
                </motion.div>

                <motion.div
                  className="bg-neutral-white rounded-3xl shadow-soft p-8 hover:shadow-medium transition-all duration-300 border border-neutral-gray-200"
                  whileHover={{ y: -2, scale: 1.02 }}
                >
                  <h4 className="font-bold text-primary-navy mb-4">Top Source Markets</h4>
                  <ul className="text-sm text-neutral-gray-600 space-y-2">
                    {travelStatistics2025.topSourceMarkets.slice(0, 4).map((market, idx) => (
                      <li key={idx} className="flex items-center">
                        <span className="text-primary-teal mr-3 font-bold">•</span>
                        <span className="leading-relaxed">{market}</span>
                      </li>
                    ))}
                  </ul>
                </motion.div>

                <motion.div
                  className="bg-neutral-white rounded-3xl shadow-soft p-8 hover:shadow-medium transition-all duration-300 border border-neutral-gray-200"
                  whileHover={{ y: -2, scale: 1.02 }}
                >
                  <h4 className="font-bold text-primary-navy mb-4">Popular Activities</h4>
                  <ul className="text-sm text-neutral-gray-600 space-y-2">
                    {travelStatistics2025.popularActivities.slice(0, 4).map((activity, idx) => (
                      <li key={idx} className="flex items-center">
                        <span className="text-primary-coral mr-3 font-bold">•</span>
                        <span className="leading-relaxed">{activity}</span>
                      </li>
                    ))}
                  </ul>
                </motion.div>

                <motion.div
                  className="bg-neutral-white rounded-3xl shadow-soft p-8 hover:shadow-medium transition-all duration-300 border border-neutral-gray-200"
                  whileHover={{ y: -2, scale: 1.02 }}
                >
                  <h4 className="font-bold text-primary-navy mb-4">Sustainability Growth</h4>
                  <div className="space-y-3 text-sm">
                    <div className="flex justify-between items-center p-3 bg-bg-pastel-sage rounded-xl">
                      <span className="text-neutral-gray-600 font-medium">Eco Accommodation</span>
                      <span className="text-accent-success font-bold text-lg">+35%</span>
                    </div>
                    <div className="flex justify-between items-center p-3 bg-bg-pastel-sage rounded-xl">
                      <span className="text-neutral-gray-600 font-medium">Wildlife Conservation</span>
                      <span className="text-accent-success font-bold text-lg">+28%</span>
                    </div>
                    <div className="flex justify-between items-center p-3 bg-bg-pastel-sage rounded-xl">
                      <span className="text-neutral-gray-600 font-medium">Community Tourism</span>
                      <span className="text-accent-success font-bold text-lg">+42%</span>
                    </div>
                  </div>
                </motion.div>
              </div>
            )}
          </motion.div>
        </AnimatePresence>
      </div>
    </section>
  )
}

export default TravelInsights2025
