import React, { useRef, useEffect, useState } from 'react'
import { motion } from 'framer-motion'
import { ChevronLeftIcon, ChevronRightIcon } from '@heroicons/react/24/outline'

interface MobileHorizontalScrollProps {
  children: React.ReactNode
  className?: string
  showArrows?: boolean
  itemWidth?: string
  gap?: string
  title?: string
}

const MobileHorizontalScroll: React.FC<MobileHorizontalScrollProps> = ({
  children,
  className = '',
  showArrows = true,
  itemWidth = 'w-80',
  gap = 'gap-4',
  title
}) => {
  const scrollRef = useRef<HTMLDivElement>(null)
  const [canScrollLeft, setCanScrollLeft] = useState(false)
  const [canScrollRight, setCanScrollRight] = useState(true)
  const [isScrolling, setIsScrolling] = useState(false)

  const checkScrollButtons = () => {
    if (!scrollRef.current) return
    
    const { scrollLeft, scrollWidth, clientWidth } = scrollRef.current
    setCanScrollLeft(scrollLeft > 0)
    setCanScrollRight(scrollLeft < scrollWidth - clientWidth - 1)
  }

  useEffect(() => {
    const scrollElement = scrollRef.current
    if (!scrollElement) return

    checkScrollButtons()
    
    const handleScroll = () => {
      checkScrollButtons()
    }

    scrollElement.addEventListener('scroll', handleScroll, { passive: true })
    window.addEventListener('resize', checkScrollButtons)

    return () => {
      scrollElement.removeEventListener('scroll', handleScroll)
      window.removeEventListener('resize', checkScrollButtons)
    }
  }, [children])

  const scroll = (direction: 'left' | 'right') => {
    if (!scrollRef.current || isScrolling) return

    setIsScrolling(true)
    const scrollAmount = scrollRef.current.clientWidth * 0.8
    const targetScroll = direction === 'left' 
      ? scrollRef.current.scrollLeft - scrollAmount
      : scrollRef.current.scrollLeft + scrollAmount

    scrollRef.current.scrollTo({
      left: targetScroll,
      behavior: 'smooth'
    })

    // Reset scrolling state after animation
    setTimeout(() => setIsScrolling(false), 500)
  }

  return (
    <div className={`relative ${className}`}>
      {title && (
        <h3 className="text-xl md:text-2xl font-bold text-gray-900 mb-4 px-4 md:px-0">
          {title}
        </h3>
      )}
      
      {/* Desktop Grid - Hidden on Mobile - 2025 Best Practice: Wider Cards */}
      <div className="hidden md:grid md:grid-cols-2 lg:grid-cols-3 gap-6">
        {children}
      </div>

      {/* Mobile Horizontal Scroll - 2025 Best Practice: Center Alignment */}
      <div className="md:hidden relative">
        {/* Scroll Container */}
        <div
          ref={scrollRef}
          className={`flex overflow-x-auto scrollbar-hide ${gap} px-6 py-2 scroll-smooth`}
          style={{
            scrollSnapType: 'x mandatory',
            WebkitOverflowScrolling: 'touch',
            scrollPaddingLeft: '1.5rem',
            scrollPaddingRight: '1.5rem'
          }}
        >
          {React.Children.map(children, (child, index) => (
            <div
              key={index}
              className={`flex-shrink-0 ${itemWidth}`}
              style={{ scrollSnapAlign: 'center' }}
            >
              {child}
            </div>
          ))}
        </div>

        {/* Navigation Arrows - 2025 Design */}
        {showArrows && (
          <>
            {/* Left Arrow */}
            <motion.button
              onClick={() => scroll('left')}
              disabled={!canScrollLeft}
              className={`absolute left-2 top-1/2 -translate-y-1/2 z-10 w-10 h-10 rounded-full bg-white/90 backdrop-blur-md shadow-lg border border-white/30 flex items-center justify-center transition-all duration-300 ${
                canScrollLeft 
                  ? 'text-gray-700 hover:bg-white hover:shadow-xl hover:scale-110' 
                  : 'text-gray-300 cursor-not-allowed opacity-50'
              }`}
              whileHover={canScrollLeft ? { scale: 1.1 } : {}}
              whileTap={canScrollLeft ? { scale: 0.95 } : {}}
              aria-label="Scroll left"
            >
              <ChevronLeftIcon className="w-5 h-5" />
            </motion.button>

            {/* Right Arrow */}
            <motion.button
              onClick={() => scroll('right')}
              disabled={!canScrollRight}
              className={`absolute right-2 top-1/2 -translate-y-1/2 z-10 w-10 h-10 rounded-full bg-white/90 backdrop-blur-md shadow-lg border border-white/30 flex items-center justify-center transition-all duration-300 ${
                canScrollRight 
                  ? 'text-gray-700 hover:bg-white hover:shadow-xl hover:scale-110' 
                  : 'text-gray-300 cursor-not-allowed opacity-50'
              }`}
              whileHover={canScrollRight ? { scale: 1.1 } : {}}
              whileTap={canScrollRight ? { scale: 0.95 } : {}}
              aria-label="Scroll right"
            >
              <ChevronRightIcon className="w-5 h-5" />
            </motion.button>
          </>
        )}

        {/* Scroll Indicators */}
        <div className="flex justify-center mt-4 space-x-2">
          {React.Children.map(children, (_, index) => {
            const isActive = index === 0 // This would need more complex logic for actual active detection
            return (
              <div
                key={index}
                className={`w-2 h-2 rounded-full transition-all duration-300 ${
                  isActive ? 'bg-primary-500 w-6' : 'bg-gray-300'
                }`}
              />
            )
          })}
        </div>
      </div>
    </div>
  )
}

export default MobileHorizontalScroll
