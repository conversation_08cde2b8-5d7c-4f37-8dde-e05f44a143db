import React, { useState, useMemo } from 'react'
import { Helmet } from 'react-helmet-async'
import { motion } from 'framer-motion'
import {
  MagnifyingGlassIcon,
  MapPinIcon,
  ClockIcon,
  CurrencyDollarIcon,
  MapIcon
} from '@heroicons/react/24/outline'
import { activities, Activity } from '@/data/activities'
import ActivitiesMap from '@/components/ActivitiesMap'
import MobileHorizontalScroll from '@/components/MobileHorizontalScroll'


const Activities: React.FC = () => {
  const [searchTerm, setSearchTerm] = useState('')
  const [selectedCategory, setSelectedCategory] = useState<string>('all')
  const [priceRange, setPriceRange] = useState<string>('all')
  const [showMap, setShowMap] = useState(false)





  const categories = ['all', 'adventure', 'cultural', 'wildlife', 'water-sports', 'wellness', 'food']
  const priceRanges = [
    { value: 'all', label: 'All Prices' },
    { value: 'budget', label: 'Under $30' },
    { value: 'mid', label: '$30 - $100' },
    { value: 'luxury', label: 'Over $100' }
  ]

  const filteredActivities = useMemo(() => {
    return activities.filter(activity => {
      const matchesSearch = activity.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                           activity.description.toLowerCase().includes(searchTerm.toLowerCase())
      const matchesCategory = selectedCategory === 'all' || activity.category === selectedCategory

      let matchesPrice = true
      if (priceRange !== 'all') {
        const price = activity.price.min
        switch (priceRange) {
          case 'budget':
            matchesPrice = price < 30
            break
          case 'mid':
            matchesPrice = price >= 30 && price <= 100
            break
          case 'luxury':
            matchesPrice = price > 100
            break
        }
      }

      return matchesSearch && matchesCategory && matchesPrice
    })
  }, [searchTerm, selectedCategory, priceRange])



  return (
    <>
      <Helmet>
        <title>Activities - Sri Lanka Tourist Guide</title>
        <meta name="description" content="Discover exciting activities and experiences in Sri Lanka." />
      </Helmet>

      {/* Hero Section */}
      <section className="bg-gradient-to-br from-green-50 to-blue-50 section-padding">
        <div className="container-max">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            className="text-center mb-12"
          >
            <h1 className="text-4xl md:text-6xl font-bold text-gray-900 mb-6">
              Exciting <span className="text-gradient">Activities</span>
            </h1>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              From thrilling adventures to cultural experiences, discover the best activities
              that Sri Lanka has to offer for every type of traveler.
            </p>
          </motion.div>

          {/* Search and Filters */}
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.2 }}
            className="bg-white rounded-2xl shadow-lg p-6 mb-12"
          >
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              {/* Search */}
              <div className="relative">
                <MagnifyingGlassIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400" />
                <input
                  type="text"
                  placeholder="Search activities..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  aria-label="Search activities"
                  className="w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent"
                />
              </div>

              {/* Category Filter */}
              <select
                value={selectedCategory}
                onChange={(e) => setSelectedCategory(e.target.value)}
                aria-label="Filter by category"
                className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent"
              >
                {categories.map(category => (
                  <option key={category} value={category}>
                    {category === 'all' ? 'All Categories' : category.charAt(0).toUpperCase() + category.slice(1).replace('-', ' ')}
                  </option>
                ))}
              </select>

              {/* Price Filter */}
              <select
                value={priceRange}
                onChange={(e) => setPriceRange(e.target.value)}
                aria-label="Filter by price range"
                className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent"
              >
                {priceRanges.map(range => (
                  <option key={range.value} value={range.value}>
                    {range.label}
                  </option>
                ))}
              </select>
            </div>
          </motion.div>
        </div>
      </section>

      {/* Interactive Map Section */}
      <section className="section-padding bg-gray-50">
        <div className="container-max">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            className="text-center mb-8"
          >
            <h2 className="text-3xl font-bold text-gray-900 mb-4">
              Explore Activities on Map
            </h2>
            <p className="text-gray-600 mb-6">
              Discover activities across Sri Lanka with our interactive map. Click on markers to see details.
            </p>
            <button
              type="button"
              onClick={() => setShowMap(!showMap)}
              className="inline-flex items-center px-6 py-3 bg-primary-600 text-white rounded-lg hover:bg-primary-700 transition-colors"
            >
              <MapIcon className="h-5 w-5 mr-2" />
              {showMap ? 'Hide Map' : 'Show Map'}
            </button>
          </motion.div>

          {showMap && (
            <motion.div
              initial={{ opacity: 0, height: 0 }}
              animate={{ opacity: 1, height: 'auto' }}
              exit={{ opacity: 0, height: 0 }}
              transition={{ duration: 0.5 }}
            >
              <ActivitiesMap
                activities={filteredActivities}
                selectedCategory={selectedCategory}
                height="600px"
                className="mb-8"
              />
            </motion.div>
          )}
        </div>
      </section>

      {/* Activities Grid */}
      <section className="section-padding bg-white">
        <div className="container-max">
          {filteredActivities.length === 0 ? (
            <motion.div
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              className="text-center py-16"
            >
              <div className="text-6xl mb-4">🔍</div>
              <h3 className="text-2xl font-semibold text-gray-900 mb-2">No activities found</h3>
              <p className="text-gray-600">Try adjusting your search criteria</p>
            </motion.div>
          ) : (
            <>
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                className="flex items-center justify-between mb-8"
              >
                <h2 className="text-2xl font-semibold text-gray-900">
                  {filteredActivities.length} activit{filteredActivities.length !== 1 ? 'ies' : 'y'} found
                </h2>
                <div className="text-sm text-gray-500">
                  {selectedCategory !== 'all' && `${selectedCategory.replace('-', ' ')} activities`}
                  {priceRange !== 'all' && ` • ${priceRanges.find(r => r.value === priceRange)?.label}`}
                </div>
              </motion.div>

              <MobileHorizontalScroll
                itemWidth="w-80"
                gap="gap-6"
                className="activities-grid"
              >
                {filteredActivities.map((activity) => (
                  <div key={activity.id} className="gsap-fade-in">
                    <ActivityCard activity={activity} />
                  </div>
                ))}
              </MobileHorizontalScroll>
            </>
          )}
        </div>
      </section>
    </>
  )
}

// Activity Card Component
interface ActivityCardProps {
  activity: Activity
}

const ActivityCard: React.FC<ActivityCardProps> = ({ activity }) => {

  const getDifficultyColor = (difficulty: string) => {
    switch (difficulty) {
      case 'easy': return 'text-green-600 bg-green-100'
      case 'moderate': return 'text-yellow-600 bg-yellow-100'
      case 'challenging': return 'text-red-600 bg-red-100'
      default: return 'text-gray-600 bg-gray-100'
    }
  }

  const getCategoryIcon = (category: string) => {
    switch (category) {
      case 'adventure': return '🏔️'
      case 'cultural': return '🏛️'
      case 'wildlife': return '🦁'
      case 'water-sports': return '🏄‍♂️'
      case 'wellness': return '🧘‍♀️'
      case 'food': return '🍽️'
      default: return '🎯'
    }
  }

  return (
    <motion.div
      whileHover={{ y: -8, scale: 1.01 }}
      transition={{ duration: 0.5 }}
      className="bg-white/90 backdrop-blur-md rounded-3xl shadow-xl border border-white/30 overflow-hidden hover:shadow-2xl transition-all duration-700 h-full group"
    >
      {/* Image */}
      <div className="relative h-64 bg-gradient-to-br from-green-400 to-blue-500 overflow-hidden">
        <div className="absolute inset-0 bg-black bg-opacity-20 group-hover:bg-opacity-10 transition-all duration-300" />

        {/* Category Badge */}
        <div className="absolute top-4 left-4">
          <span className="inline-flex items-center px-3 py-1 bg-white bg-opacity-90 rounded-full text-sm font-medium text-gray-900">
            <span className="mr-1">{getCategoryIcon(activity.category)}</span>
            {activity.category.replace('-', ' ')}
          </span>
        </div>

        {/* Difficulty Badge */}
        <div className="absolute top-4 right-4">
          <span className={`inline-block px-3 py-1 rounded-full text-xs font-medium ${getDifficultyColor(activity.difficulty)}`}>
            {activity.difficulty}
          </span>
        </div>

        {/* Price */}
        <div className="absolute bottom-4 right-4">
          <span className="inline-flex items-center bg-white bg-opacity-90 text-gray-900 text-sm font-semibold px-3 py-1 rounded-full">
            <CurrencyDollarIcon className="h-4 w-4 mr-1" />
            {activity.price.min}-{activity.price.max}
          </span>
        </div>
      </div>

      {/* Content */}
      <div className="p-6">
        <h3 className="text-xl font-semibold mb-2 text-gray-900 group-hover:text-primary-600 transition-colors">
          {activity.name}
        </h3>

        <p className="text-gray-600 mb-4 line-clamp-2">
          {activity.description}
        </p>

        {/* Meta Info */}
        <div className="grid grid-cols-2 gap-4 text-sm text-gray-500 mb-4">
          <div className="flex items-center">
            <MapPinIcon className="h-4 w-4 mr-1" />
            {activity.location}
          </div>
          <div className="flex items-center">
            <ClockIcon className="h-4 w-4 mr-1" />
            {activity.duration}
          </div>
        </div>

        {/* Highlights */}
        <div className="mb-4">
          <div className="flex flex-wrap gap-1">
            {activity.highlights.slice(0, 2).map((highlight, index) => (
              <span
                key={index}
                className="inline-block px-2 py-1 bg-gray-100 text-gray-600 text-xs rounded"
              >
                {highlight}
              </span>
            ))}
            {activity.highlights.length > 2 && (
              <span className="inline-block px-2 py-1 bg-gray-100 text-gray-600 text-xs rounded">
                +{activity.highlights.length - 2} more
              </span>
            )}
          </div>
        </div>

        {/* Best Time */}
        <div className="text-sm text-gray-500 mb-4">
          <strong>Best time:</strong> {activity.bestTime}
        </div>

        {/* Action */}
        <button
          type="button"
          className="w-full bg-primary-600 hover:bg-primary-700 text-white font-medium py-3 px-4 rounded-lg transition-colors"
        >
          Explore Activity
        </button>
      </div>
    </motion.div>
  )
}

export default Activities
