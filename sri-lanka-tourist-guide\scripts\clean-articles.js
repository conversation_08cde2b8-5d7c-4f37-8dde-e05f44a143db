const fs = require('fs');
const path = require('path');

// Read the articles file
const articlesPath = path.join(__dirname, '../src/data/articles.ts');
let articlesContent = fs.readFileSync(articlesPath, 'utf8');

// Function to clean article content
function cleanArticleContent(content) {
  // Remove metadata sections and update dates
  let cleaned = content
    // Remove Article Metadata section
    .replace(/## Article Metadata\n[\s\S]*?(?=\n## [^A]|\n---|\n# [^#]|$)/g, '')
    // Remove SEO Keywords section
    .replace(/## SEO Keywords\n[\s\S]*?(?=\n## [^S]|\n---|\n# [^#]|$)/g, '')
    // Remove Article Excerpt section (duplicate)
    .replace(/## Article Excerpt\n[\s\S]*?(?=\n---|\n# [^#]|$)/g, '')
    // Remove Images Needed sections
    .replace(/- Images Needed: \d+-\d+ high-quality photos\n/g, '')
    .replace(/\*\*Article Statistics:\*\*\n- Word Count: [\s\S]*?- Images Needed: [\s\S]*?\n/g, '')
    // Update Last Updated dates to June 25, 2025
    .replace(/- \*\*Last Updated\*\*: [^\n]+/g, '- **Last Updated**: June 25, 2025')
    .replace(/- Last Updated: [^\n]+/g, '- Last Updated: June 25, 2025')
    // Clean up multiple consecutive newlines
    .replace(/\n{3,}/g, '\n\n')
    // Remove leading/trailing whitespace
    .trim();

  return cleaned;
}

// Process the articles content
console.log('Cleaning article metadata sections...');

// Extract and process each article
const articleMatches = articlesContent.match(/"content": "([^"]*(?:\\.[^"]*)*)"/g);

if (articleMatches) {
  articleMatches.forEach((match, index) => {
    const contentMatch = match.match(/"content": "(.*)"/);
    if (contentMatch) {
      const originalContent = contentMatch[1];
      // Unescape the content
      const unescapedContent = originalContent.replace(/\\n/g, '\n').replace(/\\"/g, '"');
      
      // Clean the content
      const cleanedContent = cleanArticleContent(unescapedContent);
      
      // Escape the content back
      const escapedContent = cleanedContent.replace(/"/g, '\\"').replace(/\n/g, '\\n');
      
      // Replace in the original content
      const newMatch = `"content": "${escapedContent}"`;
      articlesContent = articlesContent.replace(match, newMatch);
    }
  });
}

// Update publish dates to June 25, 2025
articlesContent = articlesContent.replace(/"publishDate": "[^"]+"/g, '"publishDate": "2025-06-25"');

// Write the cleaned content back
fs.writeFileSync(articlesPath, articlesContent, 'utf8');

console.log('Article cleanup completed successfully!');
console.log('- Removed Article Metadata sections');
console.log('- Removed SEO Keywords sections');
console.log('- Removed duplicate Article Excerpt sections');
console.log('- Removed Images Needed references');
console.log('- Updated all Last Updated dates to June 25, 2025');
console.log('- Updated all publish dates to June 25, 2025');
