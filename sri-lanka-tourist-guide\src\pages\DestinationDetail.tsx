import React, { useState, useEffect } from 'react'
import { use<PERSON><PERSON><PERSON>, <PERSON> } from 'react-router-dom'
import { Helmet } from 'react-helmet-async'
import { motion } from 'framer-motion'
import {
  MapPinIcon,
  ClockIcon,
  CameraIcon,
  StarIcon,
  ArrowLeftIcon,
  CurrencyDollarIcon,
  ExclamationTriangleIcon,
  InformationCircleIcon
} from '@heroicons/react/24/outline'
import { destinations, Destination } from '@/data/destinations'
import InteractiveMap from '@/components/InteractiveMap'
import AnimatedSection from '@/components/AnimatedSection'

const DestinationDetail: React.FC = () => {
  const { id } = useParams<{ id: string }>()
  const [destination, setDestination] = useState<Destination | null>(null)
  const [activeTab, setActiveTab] = useState('overview')
  const [relatedDestinations, setRelatedDestinations] = useState<Destination[]>([])

  useEffect(() => {
    if (id) {
      const found = destinations.find(dest => dest.id === id)
      setDestination(found || null)

      if (found) {
        // Find related destinations (same category or province)
        const related = destinations
          .filter(dest =>
            dest.id !== id &&
            (dest.category === found.category || dest.province === found.province)
          )
          .slice(0, 3)
        setRelatedDestinations(related)
      }
    }
  }, [id])

  if (!destination) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <h1 className="text-2xl font-bold text-gray-900 mb-4">Destination Not Found</h1>
          <Link to="/destinations" className="btn-primary">
            Back to Destinations
          </Link>
        </div>
      </div>
    )
  }

  const tabs = [
    { id: 'overview', name: 'Overview', icon: InformationCircleIcon },
    { id: 'activities', name: 'Activities', icon: StarIcon },
    { id: 'practical', name: 'Practical Info', icon: MapPinIcon },
    { id: 'photos', name: 'Photography', icon: CameraIcon }
  ]

  return (
    <>
      <Helmet>
        <title>{destination.name} - Complete Travel Guide | Sri Lanka Tourist Guide</title>
        <meta name="description" content={destination.description} />
        <meta property="og:title" content={`${destination.name} - Complete Travel Guide`} />
        <meta property="og:description" content={destination.shortDescription} />
        <meta property="og:image" content={destination.images[0]} />
        <script type="application/ld+json">
          {JSON.stringify({
            "@context": "https://schema.org",
            "@type": "TouristDestination",
            "name": destination.name,
            "description": destination.description,
            "image": destination.images,
            "geo": {
              "@type": "GeoCoordinates",
              "latitude": destination.coordinates.lat,
              "longitude": destination.coordinates.lng
            },
            "address": {
              "@type": "PostalAddress",
              "addressRegion": `${destination.province} Province`,
              "addressCountry": "Sri Lanka"
            }
          })}
        </script>
      </Helmet>

      <div className="min-h-screen bg-white">
        {/* Hero Section */}
        <section className="relative h-[70vh] overflow-hidden">
          <div className="absolute inset-0">
            <img
              src={destination.images[0]}
              alt={destination.name}
              className="w-full h-full object-cover"
            />
            <div className="absolute inset-0 bg-gradient-to-t from-black/60 via-black/20 to-transparent" />
          </div>

          <div className="relative h-full flex items-end">
            <div className="container-max pb-16">
              <motion.div
                initial={{ opacity: 0, y: 30 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.8 }}
              >
                <Link
                  to="/destinations"
                  className="inline-flex items-center text-white/80 hover:text-white mb-6 transition-colors"
                >
                  <ArrowLeftIcon className="h-5 w-5 mr-2" />
                  Back to Destinations
                </Link>

                <h1 className="text-4xl md:text-6xl font-bold text-white mb-4">
                  {destination.name}
                </h1>

                <p className="text-xl text-white/90 mb-6 max-w-3xl">
                  {destination.shortDescription}
                </p>

                <div className="flex flex-wrap gap-4 text-white/80">
                  <div className="flex items-center space-x-2">
                    <MapPinIcon className="h-5 w-5" />
                    <span>{destination.province} Province</span>
                  </div>
                  <div className="flex items-center space-x-2">
                    <ClockIcon className="h-5 w-5" />
                    <span>{destination.duration}</span>
                  </div>
                  <div className="flex items-center space-x-2">
                    <span className={`px-3 py-1 rounded-full text-sm font-medium ${
                      destination.difficulty === 'easy' ? 'bg-green-500/20 text-green-300' :
                      destination.difficulty === 'moderate' ? 'bg-yellow-500/20 text-yellow-300' :
                      'bg-red-500/20 text-red-300'
                    }`}>
                      {destination.difficulty}
                    </span>
                  </div>
                </div>
              </motion.div>
            </div>
          </div>
        </section>

        {/* Navigation Tabs */}
        <section className="sticky top-0 z-40 bg-white border-b border-gray-200">
          <div className="container-max">
            <nav className="flex space-x-8 overflow-x-auto">
              {tabs.map((tab) => {
                const Icon = tab.icon
                return (
                  <button
                    key={tab.id}
                    type="button"
                    onClick={() => setActiveTab(tab.id)}
                    className={`flex items-center space-x-2 py-4 px-2 border-b-2 font-medium text-sm whitespace-nowrap transition-colors ${
                      activeTab === tab.id
                        ? 'border-primary-500 text-primary-600'
                        : 'border-transparent text-gray-500 hover:text-gray-700'
                    }`}
                  >
                    <Icon className="h-5 w-5" />
                    <span>{tab.name}</span>
                  </button>
                )
              })}
            </nav>
          </div>
        </section>

        {/* Content Sections */}
        <div className="container-max py-12">
          {/* Overview Tab */}
          {activeTab === 'overview' && (
            <AnimatedSection>
              <div className="grid grid-cols-1 lg:grid-cols-3 gap-12">
                {/* Main Content */}
                <div className="lg:col-span-2 space-y-8">
                  <div>
                    <h2 className="text-3xl font-bold text-gray-900 mb-6">About {destination.name}</h2>
                    <p className="text-lg text-gray-600 leading-relaxed mb-6">
                      {destination.description}
                    </p>

                    {destination.historicalBackground && (
                      <div className="bg-amber-50 border-l-4 border-amber-400 p-6 rounded-r-lg">
                        <h3 className="text-lg font-semibold text-amber-900 mb-3">Historical Background</h3>
                        <p className="text-amber-800">{destination.historicalBackground}</p>
                      </div>
                    )}
                  </div>

                  <div>
                    <h3 className="text-2xl font-bold text-gray-900 mb-4">Highlights</h3>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      {destination.highlights.map((highlight, index) => (
                        <motion.div
                          key={index}
                          initial={{ opacity: 0, x: -20 }}
                          whileInView={{ opacity: 1, x: 0 }}
                          transition={{ duration: 0.5, delay: index * 0.1 }}
                          viewport={{ once: true }}
                          className="flex items-start space-x-3 p-4 bg-gray-50 rounded-lg"
                        >
                          <StarIcon className="h-5 w-5 text-primary-500 mt-0.5 flex-shrink-0" />
                          <span className="text-gray-700">{highlight}</span>
                        </motion.div>
                      ))}
                    </div>
                  </div>



                  {destination.nearbyAttractions && destination.nearbyAttractions.length > 0 && (
                    <div>
                      <h3 className="text-2xl font-bold text-gradient-nature mb-4">Nearby Attractions</h3>
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        {destination.nearbyAttractions.map((attraction, index) => (
                          <div key={index} className="flex items-center space-x-3 p-4 bg-pastel-mint rounded-2xl border border-gray-100 hover:shadow-gentle transition-all duration-300">
                            <MapPinIcon className="h-5 w-5 text-muted-green" />
                            <span className="text-gray-700 font-medium">{attraction}</span>
                          </div>
                        ))}
                      </div>
                    </div>
                  )}
                </div>

                {/* Sidebar */}
                <div className="space-y-6">
                  {/* 2025 Enhanced Interactive Map */}
                  <div className="bg-gradient-warm border border-neutral-gray-200 rounded-3xl p-6 shadow-soft">
                    <h3 className="text-xl font-bold text-primary-navy mb-4 flex items-center">
                      <MapPinIcon className="h-6 w-6 text-primary-coral mr-2" />
                      Explore {destination.name}
                    </h3>
                    <div className="h-80 rounded-2xl overflow-hidden shadow-medium mb-4">
                      <InteractiveMap
                        height="100%"
                        center={[destination.coordinates.lat, destination.coordinates.lng]}
                        destinations={[destination]}
                        showLocationDetails={true}
                        showNearbyAttractions={true}
                        showActivities={true}
                        showRestaurants={true}
                        showViewpoints={true}
                      />
                    </div>
                    <div className="text-center">
                      <p className="text-sm text-neutral-gray-600 leading-relaxed">
                        Interactive map showing {destination.name} with nearby attractions, activities, restaurants, and viewpoints
                      </p>
                    </div>
                  </div>

                  {/* Quick Info */}
                  <div className="bg-neutral-white border border-neutral-gray-200 rounded-3xl p-6 shadow-soft">
                    <h3 className="text-lg font-semibold text-primary-navy mb-4">Quick Info</h3>
                    <div className="space-y-4">
                      <div className="flex items-center justify-between p-3 bg-bg-pastel-teal rounded-xl">
                        <span className="text-neutral-gray-600 font-medium">Best Time to Visit</span>
                        <span className="text-primary-teal font-semibold">{destination.bestTimeToVisit}</span>
                      </div>
                      <div className="flex items-center justify-between p-3 bg-bg-pastel-amber rounded-xl">
                        <span className="text-neutral-gray-600 font-medium">Duration</span>
                        <span className="text-secondary-amber font-semibold">{destination.duration}</span>
                      </div>
                      <div className="flex items-center justify-between p-3 bg-bg-pastel-lavender rounded-xl">
                        <span className="text-neutral-gray-600 font-medium">Difficulty</span>
                        <span className={`px-3 py-1 rounded-full text-sm font-semibold ${
                          destination.difficulty === 'easy' ? 'bg-accent-success text-white' :
                          destination.difficulty === 'moderate' ? 'bg-accent-warning text-white' :
                          'bg-accent-error text-white'
                        }`}>
                          {destination.difficulty}
                        </span>
                      </div>
                      {destination.entryFee && (
                        <div className="flex items-center justify-between p-3 bg-bg-pastel-coral rounded-xl">
                          <span className="text-neutral-gray-600 font-medium">Entry Fee</span>
                          <span className="text-primary-coral font-semibold">{destination.entryFee}</span>
                        </div>
                      )}
                    </div>
                  </div>

                  {/* Weather Info */}
                  {destination.weatherInfo && (
                    <div className="bg-gradient-cool border border-primary-teal/20 rounded-3xl p-6 shadow-soft">
                      <h3 className="text-lg font-semibold text-primary-navy mb-4 flex items-center">
                        <span className="text-2xl mr-2">🌤️</span>
                        Weather Information
                      </h3>
                      <div className="space-y-3">
                        <div className="flex items-center justify-between p-3 bg-neutral-white/50 rounded-xl">
                          <span className="text-neutral-gray-600 font-medium">Temperature:</span>
                          <span className="text-primary-teal font-semibold">{destination.weatherInfo.temperature}</span>
                        </div>
                        <div className="flex items-center justify-between p-3 bg-neutral-white/50 rounded-xl">
                          <span className="text-neutral-gray-600 font-medium">Rainfall:</span>
                          <span className="text-primary-teal font-semibold">{destination.weatherInfo.rainfall}</span>
                        </div>
                        <div className="flex items-center justify-between p-3 bg-neutral-white/50 rounded-xl">
                          <span className="text-neutral-gray-600 font-medium">Humidity:</span>
                          <span className="text-primary-teal font-semibold">{destination.weatherInfo.humidity}</span>
                        </div>
                      </div>
                    </div>
                  )}
                </div>
              </div>
            </AnimatedSection>
          )}

          {/* Activities Tab */}
          {activeTab === 'activities' && (
            <AnimatedSection>
              <div className="max-w-4xl">
                <h2 className="text-3xl font-bold text-gray-900 mb-8">Things to Do</h2>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  {destination.activities.map((activity, index) => (
                    <motion.div
                      key={index}
                      initial={{ opacity: 0, y: 20 }}
                      whileInView={{ opacity: 1, y: 0 }}
                      transition={{ duration: 0.5, delay: index * 0.1 }}
                      viewport={{ once: true }}
                      className="bg-white border border-gray-200 rounded-xl p-6 hover:shadow-lg transition-shadow"
                    >
                      <div className="flex items-start space-x-4">
                        <div className="p-3 bg-primary-100 rounded-lg">
                          <StarIcon className="h-6 w-6 text-primary-600" />
                        </div>
                        <div>
                          <h3 className="text-lg font-semibold text-gray-900 mb-2">{activity}</h3>
                          <p className="text-gray-600 text-sm">
                            Experience this amazing activity during your visit to {destination.name}.
                          </p>
                        </div>
                      </div>
                    </motion.div>
                  ))}
                </div>
              </div>
            </AnimatedSection>
          )}

          {/* Practical Info Tab */}
          {activeTab === 'practical' && (
            <AnimatedSection>
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-12">
                <div className="space-y-8">
                  {/* Transportation */}
                  <div>
                    <h3 className="text-2xl font-bold text-gray-900 mb-4">Getting There</h3>
                    <div className="space-y-4">
                      {destination.transportation.fromColombo && (
                        <div className="p-4 bg-gray-50 rounded-lg">
                          <h4 className="font-semibold text-gray-900 mb-2">From Colombo</h4>
                          <p className="text-gray-600">{destination.transportation.fromColombo}</p>
                        </div>
                      )}
                      {destination.transportation.fromAirport && (
                        <div className="p-4 bg-gray-50 rounded-lg">
                          <h4 className="font-semibold text-gray-900 mb-2">From Airport</h4>
                          <p className="text-gray-600">{destination.transportation.fromAirport}</p>
                        </div>
                      )}
                      <div className="p-4 bg-gray-50 rounded-lg">
                        <h4 className="font-semibold text-gray-900 mb-2">Local Transport</h4>
                        <div className="flex flex-wrap gap-2">
                          {destination.transportation.localTransport.map((transport, index) => (
                            <span
                              key={index}
                              className="px-3 py-1 bg-white border border-gray-200 rounded-full text-sm text-gray-700"
                            >
                              {transport}
                            </span>
                          ))}
                        </div>
                      </div>
                    </div>
                  </div>

                  {/* Tips */}
                  <div>
                    <h3 className="text-2xl font-bold text-gray-900 mb-4">Travel Tips</h3>
                    <div className="space-y-3">
                      {destination.tips.map((tip, index) => (
                        <div key={index} className="flex items-start space-x-3 p-4 bg-green-50 rounded-lg">
                          <InformationCircleIcon className="h-5 w-5 text-green-600 mt-0.5 flex-shrink-0" />
                          <span className="text-green-800">{tip}</span>
                        </div>
                      ))}
                    </div>
                  </div>

                  {/* Safety Info */}
                  {destination.safetyInfo && destination.safetyInfo.length > 0 && (
                    <div>
                      <h3 className="text-2xl font-bold text-gray-900 mb-4">Safety Information</h3>
                      <div className="space-y-3">
                        {destination.safetyInfo.map((info, index) => (
                          <div key={index} className="flex items-start space-x-3 p-4 bg-red-50 rounded-lg">
                            <ExclamationTriangleIcon className="h-5 w-5 text-red-600 mt-0.5 flex-shrink-0" />
                            <span className="text-red-800">{info}</span>
                          </div>
                        ))}
                      </div>
                    </div>
                  )}
                </div>

                <div className="space-y-8">
                  {/* Accommodation */}
                  {destination.accommodation && (
                    <div>
                      <h3 className="text-2xl font-bold text-gray-900 mb-4">Where to Stay</h3>
                      <div className="space-y-4">
                        {destination.accommodation.budget && destination.accommodation.budget.length > 0 && (
                          <div className="p-4 border border-gray-200 rounded-lg">
                            <h4 className="font-semibold text-gray-900 mb-2 flex items-center">
                              <CurrencyDollarIcon className="h-5 w-5 text-green-600 mr-2" />
                              Budget Options
                            </h4>
                            <ul className="space-y-1">
                              {destination.accommodation.budget.map((place, index) => (
                                <li key={index} className="text-gray-600">{place}</li>
                              ))}
                            </ul>
                          </div>
                        )}
                        {destination.accommodation.midRange && destination.accommodation.midRange.length > 0 && (
                          <div className="p-4 border border-gray-200 rounded-lg">
                            <h4 className="font-semibold text-gray-900 mb-2 flex items-center">
                              <CurrencyDollarIcon className="h-5 w-5 text-yellow-600 mr-2" />
                              Mid-Range Options
                            </h4>
                            <ul className="space-y-1">
                              {destination.accommodation.midRange.map((place, index) => (
                                <li key={index} className="text-gray-600">{place}</li>
                              ))}
                            </ul>
                          </div>
                        )}
                        {destination.accommodation.luxury && destination.accommodation.luxury.length > 0 && (
                          <div className="p-4 border border-gray-200 rounded-lg">
                            <h4 className="font-semibold text-gray-900 mb-2 flex items-center">
                              <CurrencyDollarIcon className="h-5 w-5 text-purple-600 mr-2" />
                              Luxury Options
                            </h4>
                            <ul className="space-y-1">
                              {destination.accommodation.luxury.map((place, index) => (
                                <li key={index} className="text-gray-600">{place}</li>
                              ))}
                            </ul>
                          </div>
                        )}
                      </div>
                    </div>
                  )}


                </div>
              </div>
            </AnimatedSection>
          )}

          {/* Photography Tab */}
          {activeTab === 'photos' && (
            <AnimatedSection>
              <div className="max-w-4xl">
                <h2 className="text-3xl font-bold text-gray-900 mb-8">Photography Guide</h2>

                {destination.photographyTips && destination.photographyTips.length > 0 && (
                  <div className="mb-8">
                    <h3 className="text-xl font-semibold text-gray-900 mb-4">Photography Tips</h3>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      {destination.photographyTips.map((tip, index) => (
                        <div key={index} className="flex items-start space-x-3 p-4 bg-purple-50 rounded-lg">
                          <CameraIcon className="h-5 w-5 text-purple-600 mt-0.5 flex-shrink-0" />
                          <span className="text-purple-800">{tip}</span>
                        </div>
                      ))}
                    </div>
                  </div>
                )}

                {/* Image Gallery */}
                <div>
                  <h3 className="text-xl font-semibold text-gray-900 mb-4">Photo Gallery</h3>
                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                    {destination.images.map((image, index) => (
                      <motion.div
                        key={index}
                        initial={{ opacity: 0, scale: 0.9 }}
                        whileInView={{ opacity: 1, scale: 1 }}
                        transition={{ duration: 0.5, delay: index * 0.1 }}
                        viewport={{ once: true }}
                        className="aspect-square rounded-lg overflow-hidden shadow-md hover:shadow-lg transition-shadow"
                      >
                        <img
                          src={image}
                          alt={`${destination.name} - Photo ${index + 1}`}
                          className="w-full h-full object-cover hover:scale-105 transition-transform duration-300"
                        />
                      </motion.div>
                    ))}
                  </div>
                </div>
              </div>
            </AnimatedSection>
          )}
        </div>

        {/* Related Destinations */}
        {relatedDestinations.length > 0 && (
          <section className="section-padding bg-gray-50">
            <div className="container-max">
              <h2 className="text-3xl font-bold text-gray-900 mb-8 text-center">
                More Destinations Like This
              </h2>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
                {relatedDestinations.map((dest) => (
                  <motion.div
                    key={dest.id}
                    initial={{ opacity: 0, y: 30 }}
                    whileInView={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.6 }}
                    viewport={{ once: true }}
                    className="bg-white rounded-xl overflow-hidden shadow-lg hover:shadow-xl transition-shadow"
                  >
                    <div className="aspect-video overflow-hidden">
                      <img
                        src={dest.images[0]}
                        alt={dest.name}
                        className="w-full h-full object-cover hover:scale-105 transition-transform duration-300"
                      />
                    </div>
                    <div className="p-6">
                      <h3 className="text-xl font-bold text-gray-900 mb-2">{dest.name}</h3>
                      <p className="text-gray-600 mb-4">{dest.shortDescription}</p>
                      <Link
                        to={`/destinations/${dest.id}`}
                        className="btn-primary w-full text-center"
                      >
                        Explore {dest.name}
                      </Link>
                    </div>
                  </motion.div>
                ))}
              </div>
            </div>
          </section>
        )}
      </div>
    </>
  )
}

export default DestinationDetail
