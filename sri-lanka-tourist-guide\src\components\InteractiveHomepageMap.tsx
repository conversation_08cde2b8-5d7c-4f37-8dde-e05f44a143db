import React, { useEffect, useRef, useState } from 'react'
import { motion } from 'framer-motion'
import { gsap } from 'gsap'
import { ScrollTrigger } from 'gsap/ScrollTrigger'
import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, Popup, useMap } from 'react-leaflet'
import { Icon } from 'leaflet'
import { destinations } from '@/data/destinations'
import { Link } from 'react-router-dom'

gsap.registerPlugin(ScrollTrigger)

// Custom marker icons
const createCustomIcon = (category: string) => {
  const iconColors: Record<string, string> = {
    'cultural': '#8b5cf6',
    'nature': '#10b981',
    'beach': '#3b82f6',
    'adventure': '#ef4444',
    'city': '#f59e0b'
  }

  const color = iconColors[category] || '#6b7280'

  // Use URL encoding instead of base64 to avoid emoji encoding issues
  const svgString = `<svg width="40" height="40" viewBox="0 0 40 40" xmlns="http://www.w3.org/2000/svg">
    <circle cx="20" cy="20" r="18" fill="${color}" stroke="#ffffff" stroke-width="3"/>
    <circle cx="20" cy="20" r="8" fill="#ffffff"/>
  </svg>`

  return new Icon({
    iconUrl: `data:image/svg+xml;charset=utf-8,${encodeURIComponent(svgString)}`,
    iconSize: [40, 40],
    iconAnchor: [20, 40],
    popupAnchor: [0, -40]
  })
}

interface MapControllerProps {
  selectedDestination: string | null
}

const MapController: React.FC<MapControllerProps> = ({ selectedDestination }) => {
  const map = useMap()
  
  useEffect(() => {
    if (selectedDestination) {
      const destination = destinations.find(d => d.id === selectedDestination)
      if (destination) {
        map.flyTo([destination.coordinates.lat, destination.coordinates.lng], 10, {
          duration: 1.5
        })
      }
    }
  }, [selectedDestination, map])
  
  return null
}

const InteractiveHomepageMap: React.FC = () => {
  const mapRef = useRef<HTMLDivElement>(null)
  const [selectedDestination, setSelectedDestination] = useState<string | null>(null)
  const [hoveredDestination, setHoveredDestination] = useState<string | null>(null)

  useEffect(() => {
    if (!mapRef.current) return

    // Animate map container on scroll
    gsap.fromTo(mapRef.current, 
      { opacity: 0, y: 50 },
      {
        opacity: 1,
        y: 0,
        duration: 1,
        scrollTrigger: {
          trigger: mapRef.current,
          start: 'top 80%',
          end: 'bottom 20%',
          toggleActions: 'play none none reverse'
        }
      }
    )

    return () => {
      ScrollTrigger.getAll().forEach(trigger => {
        if (trigger.trigger === mapRef.current) {
          trigger.kill()
        }
      })
    }
  }, [])

  const featuredDestinations = destinations.slice(0, 8)

  return (
    <section className="section-padding bg-white">
      <div className="container-max">
        <div className="text-center mb-12">
          <motion.h2
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
            className="text-3xl md:text-4xl font-bold text-gray-900 mb-6"
          >
            Explore Sri Lanka Interactively
          </motion.h2>
          <motion.p
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.2 }}
            viewport={{ once: true }}
            className="text-lg text-gray-600 max-w-3xl mx-auto"
          >
            Click on destinations to explore or hover over the list to see their locations on the map
          </motion.p>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Destinations List */}
          <div className="lg:col-span-1 space-y-4">
            <h3 className="text-xl font-semibold text-gray-900 mb-4">Top Destinations</h3>
            <div className="space-y-3 max-h-96 overflow-y-auto">
              {featuredDestinations.map((destination, index) => (
                <motion.div
                  key={destination.id}
                  initial={{ opacity: 0, x: -20 }}
                  whileInView={{ opacity: 1, x: 0 }}
                  transition={{ duration: 0.5, delay: index * 0.1 }}
                  viewport={{ once: true }}
                  className={`p-4 rounded-lg border-2 cursor-pointer transition-all duration-300 ${
                    selectedDestination === destination.id
                      ? 'border-blue-500 bg-blue-50'
                      : hoveredDestination === destination.id
                      ? 'border-blue-300 bg-blue-25'
                      : 'border-gray-200 hover:border-gray-300'
                  }`}
                  onClick={() => setSelectedDestination(destination.id)}
                  onMouseEnter={() => setHoveredDestination(destination.id)}
                  onMouseLeave={() => setHoveredDestination(null)}
                >
                  <div className="flex items-start space-x-3">
                    <div className="text-2xl">{destination.category === 'cultural' ? '🏛️' :
                                                destination.category === 'nature' ? '🌿' :
                                                destination.category === 'beach' ? '🏖️' :
                                                destination.category === 'adventure' ? '⛰️' :
                                                destination.category === 'city' ? '🏙️' : '📍'}</div>
                    <div className="flex-1">
                      <h4 className="font-semibold text-gray-900 mb-1">{destination.name}</h4>
                      <p className="text-sm text-gray-600 mb-2 line-clamp-2">{destination.shortDescription}</p>
                      <div className="flex items-center justify-between">
                        <span className="text-xs text-blue-600 font-medium">{destination.category}</span>
                        <Link
                          to={`/destinations/${destination.id}`}
                          className="text-xs text-gray-500 hover:text-blue-600 transition-colors px-3 py-2 min-h-[44px] flex items-center"
                          onClick={(e) => e.stopPropagation()}
                        >
                          Learn More →
                        </Link>
                      </div>
                    </div>
                  </div>
                </motion.div>
              ))}
            </div>
          </div>

          {/* Interactive Map */}
          <div className="lg:col-span-2">
            <div ref={mapRef} className="h-96 lg:h-[500px] rounded-xl overflow-hidden shadow-lg border border-gray-200">
              <MapContainer
                center={[7.8731, 80.7718]} // Center of Sri Lanka
                zoom={7}
                style={{ height: '100%', width: '100%' }}
                className="z-10"
              >
                <TileLayer
                  url="https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png"
                  attribution='&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors'
                />
                
                <MapController selectedDestination={selectedDestination} />
                
                {featuredDestinations.map((destination) => (
                  <Marker
                    key={destination.id}
                    position={[destination.coordinates.lat, destination.coordinates.lng]}
                    icon={createCustomIcon(destination.category)}
                    eventHandlers={{
                      click: () => setSelectedDestination(destination.id),
                      mouseover: () => setHoveredDestination(destination.id),
                      mouseout: () => setHoveredDestination(null)
                    }}
                  >
                    <Popup>
                      <div className="p-2 min-w-[200px]">
                        <h4 className="font-semibold text-gray-900 mb-2">{destination.name}</h4>
                        <p className="text-sm text-gray-600 mb-3">{destination.shortDescription}</p>
                        <div className="flex items-center justify-between">
                          <span className="text-xs bg-blue-100 text-blue-700 px-2 py-1 rounded-full">
                            {destination.category}
                          </span>
                          <Link
                            to={`/destinations/${destination.id}`}
                            className="text-sm text-blue-600 hover:text-blue-700 font-medium"
                          >
                            Explore →
                          </Link>
                        </div>
                      </div>
                    </Popup>
                  </Marker>
                ))}
              </MapContainer>
            </div>
            
            {/* Map Legend */}
            <div className="mt-4 p-4 bg-gray-50 rounded-lg">
              <h4 className="text-sm font-semibold text-gray-900 mb-3">Map Legend</h4>
              <div className="grid grid-cols-2 md:grid-cols-3 gap-2 text-xs">
                <div className="flex items-center space-x-2">
                  <span>🏛️</span>
                  <span className="text-gray-600">Cultural</span>
                </div>
                <div className="flex items-center space-x-2">
                  <span>🌿</span>
                  <span className="text-gray-600">Nature</span>
                </div>
                <div className="flex items-center space-x-2">
                  <span>🏖️</span>
                  <span className="text-gray-600">Beach</span>
                </div>
                <div className="flex items-center space-x-2">
                  <span>⛰️</span>
                  <span className="text-gray-600">Adventure</span>
                </div>
                <div className="flex items-center space-x-2">
                  <span>🏙️</span>
                  <span className="text-gray-600">City</span>
                </div>
              </div>
            </div>
          </div>
        </div>

        <div className="text-center mt-8">
          <Link to="/destinations" className="btn-primary">
            View All Destinations
          </Link>
        </div>
      </div>
    </section>
  )
}

export default InteractiveHomepageMap
