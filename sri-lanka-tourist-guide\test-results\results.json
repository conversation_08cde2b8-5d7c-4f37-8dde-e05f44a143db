{"config": {"configFile": "E:\\Augment Code Testing\\sri-lanka-tourist-guide\\playwright.config.ts", "rootDir": "E:/Augment Code Testing/sri-lanka-tourist-guide/tests", "forbidOnly": false, "fullyParallel": true, "globalSetup": null, "globalTeardown": null, "globalTimeout": 0, "grep": {}, "grepInvert": null, "maxFailures": 0, "metadata": {"actualWorkers": 2}, "preserveOutput": "always", "reporter": [["html", null], ["json", {"outputFile": "test-results/results.json"}], ["junit", {"outputFile": "test-results/results.xml"}]], "reportSlowTests": {"max": 5, "threshold": 300000}, "quiet": false, "projects": [{"outputDir": "E:/Augment Code Testing/sri-lanka-tourist-guide/test-results", "repeatEach": 1, "retries": 0, "metadata": {"actualWorkers": 2}, "id": "chromium", "name": "chromium", "testDir": "E:/Augment Code Testing/sri-lanka-tourist-guide/tests", "testIgnore": [], "testMatch": ["**/*.@(spec|test).?(c|m)[jt]s?(x)"], "timeout": 30000}, {"outputDir": "E:/Augment Code Testing/sri-lanka-tourist-guide/test-results", "repeatEach": 1, "retries": 0, "metadata": {"actualWorkers": 2}, "id": "firefox", "name": "firefox", "testDir": "E:/Augment Code Testing/sri-lanka-tourist-guide/tests", "testIgnore": [], "testMatch": ["**/*.@(spec|test).?(c|m)[jt]s?(x)"], "timeout": 30000}, {"outputDir": "E:/Augment Code Testing/sri-lanka-tourist-guide/test-results", "repeatEach": 1, "retries": 0, "metadata": {"actualWorkers": 2}, "id": "webkit", "name": "webkit", "testDir": "E:/Augment Code Testing/sri-lanka-tourist-guide/tests", "testIgnore": [], "testMatch": ["**/*.@(spec|test).?(c|m)[jt]s?(x)"], "timeout": 30000}, {"outputDir": "E:/Augment Code Testing/sri-lanka-tourist-guide/test-results", "repeatEach": 1, "retries": 0, "metadata": {"actualWorkers": 2}, "id": "Mobile Chrome", "name": "Mobile Chrome", "testDir": "E:/Augment Code Testing/sri-lanka-tourist-guide/tests", "testIgnore": [], "testMatch": ["**/*.@(spec|test).?(c|m)[jt]s?(x)"], "timeout": 30000}, {"outputDir": "E:/Augment Code Testing/sri-lanka-tourist-guide/test-results", "repeatEach": 1, "retries": 0, "metadata": {"actualWorkers": 2}, "id": "Mobile Safari", "name": "Mobile Safari", "testDir": "E:/Augment Code Testing/sri-lanka-tourist-guide/tests", "testIgnore": [], "testMatch": ["**/*.@(spec|test).?(c|m)[jt]s?(x)"], "timeout": 30000}], "shard": null, "updateSnapshots": "missing", "updateSourceMethod": "patch", "version": "1.53.1", "workers": 2, "webServer": {"command": "npm run dev", "url": "http://localhost:5173", "reuseExistingServer": true, "timeout": 120000}}, "suites": [{"title": "mobile-ux-analysis.spec.ts", "file": "mobile-ux-analysis.spec.ts", "column": 0, "line": 0, "specs": [], "suites": [{"title": "Mobile UX Analysis - 2025 Standards", "file": "mobile-ux-analysis.spec.ts", "line": 3, "column": 6, "specs": [{"title": "Mobile Analysis: Activities Page", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 0, "parallelIndex": 0, "status": "passed", "duration": 8883, "errors": [], "stdout": [{"text": "\n=== ANALYZING ACTIVITIES PAGE ===\n"}, {"text": "✓ Screenshot captured for Activities page\n"}, {"text": "Navigation elements found: 2\n"}, {"text": "\n--- Mobile Issues Analysis for Activities ---\n"}, {"text": "⚠️  Found 15 button pairs with insufficient spacing (<8px)\n"}, {"text": "\n--- Touch Target Analysis for Activities ---\n"}, {"text": "⚠️  Found 34 touch targets smaller than 44px (2025 standard)\n"}, {"text": "\n--- Card Layout Analysis for Activities ---\n"}, {"text": "Found 32 potential card containers\n"}, {"text": "⚠️  Card layout issues found:\n"}, {"text": "   - Card 2: Not visible (0 dimensions)\n"}, {"text": "   - Card 3: Not visible (0 dimensions)\n"}, {"text": "   - Card 4: Not visible (0 dimensions)\n"}, {"text": "   - Card 5: Not visible (0 dimensions)\n"}, {"text": "   - Card 6: Not visible (0 dimensions)\n"}, {"text": "   - Card 7: Not visible (0 dimensions)\n"}, {"text": "   - Card 8: Not visible (0 dimensions)\n"}, {"text": "   - Card 9: Not visible (0 dimensions)\n"}, {"text": "   - Card 10: Not visible (0 dimensions)\n"}, {"text": "   - Card 11: Not visible (0 dimensions)\n"}, {"text": "   - Card 12: Not visible (0 dimensions)\n"}, {"text": "   - Card 13: Not visible (0 dimensions)\n"}, {"text": "   - Card 14: Not visible (0 dimensions)\n"}, {"text": "   - Card 15: Not visible (0 dimensions)\n"}, {"text": "   - Card 16: Not visible (0 dimensions)\n"}, {"text": "   - Card 17: Not visible (0 dimensions)\n"}, {"text": "   - Card 18: Not visible (0 dimensions)\n"}, {"text": "   - Card 19: Not visible (0 dimensions)\n"}, {"text": "   - Card 20: Not visible (0 dimensions)\n"}, {"text": "   - Card 21: Not visible (0 dimensions)\n"}, {"text": "   - Card 22: Not visible (0 dimensions)\n"}, {"text": "   - Card 23: Not visible (0 dimensions)\n"}, {"text": "   - Card 24: Not visible (0 dimensions)\n"}, {"text": "   - Card 25: Not visible (0 dimensions)\n"}, {"text": "   - Card 26: Not visible (0 dimensions)\n"}, {"text": "   - Card 27: Not visible (0 dimensions)\n"}, {"text": "   - Card 28: Not visible (0 dimensions)\n"}, {"text": "   - Card 29: Not visible (0 dimensions)\n"}, {"text": "\n--- Horizontal Scrolling Analysis for Activities ---\n"}, {"text": "✓ No unwanted horizontal scrolling detected\n"}, {"text": "Found 1 intentional horizontal scroll containers\n"}], "stderr": [], "retry": 0, "startTime": "2025-06-25T05:56:34.985Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "0f6969d158d38e126276-93b92ea0bb88b2c3d8a0", "file": "mobile-ux-analysis.spec.ts", "line": 22, "column": 5}, {"title": "Mobile Analysis: Activities Page", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "firefox", "projectName": "firefox", "results": [{"workerIndex": 1, "parallelIndex": 1, "status": "passed", "duration": 14779, "errors": [], "stdout": [{"text": "\n=== ANALYZING ACTIVITIES PAGE ===\n"}, {"text": "✓ Screenshot captured for Activities page\n"}, {"text": "Navigation elements found: 2\n"}, {"text": "\n--- Mobile Issues Analysis for Activities ---\n"}, {"text": "⚠️  Found 15 button pairs with insufficient spacing (<8px)\n"}, {"text": "\n--- Touch Target Analysis for Activities ---\n"}, {"text": "⚠️  Found 34 touch targets smaller than 44px (2025 standard)\n"}, {"text": "\n--- Card Layout Analysis for Activities ---\n"}, {"text": "Found 32 potential card containers\n"}, {"text": "⚠️  Card layout issues found:\n"}, {"text": "   - Card 2: Not visible (0 dimensions)\n"}, {"text": "   - Card 3: Not visible (0 dimensions)\n"}, {"text": "   - Card 4: Not visible (0 dimensions)\n"}, {"text": "   - Card 5: Not visible (0 dimensions)\n"}, {"text": "   - Card 6: Not visible (0 dimensions)\n"}, {"text": "   - Card 7: Not visible (0 dimensions)\n"}, {"text": "   - Card 8: Not visible (0 dimensions)\n"}, {"text": "   - Card 9: Not visible (0 dimensions)\n"}, {"text": "   - Card 10: Not visible (0 dimensions)\n"}, {"text": "   - Card 11: Not visible (0 dimensions)\n"}, {"text": "   - Card 12: Not visible (0 dimensions)\n"}, {"text": "   - Card 13: Not visible (0 dimensions)\n"}, {"text": "   - Card 14: Not visible (0 dimensions)\n"}, {"text": "   - Card 15: Not visible (0 dimensions)\n"}, {"text": "   - Card 16: Not visible (0 dimensions)\n"}, {"text": "   - Card 17: Not visible (0 dimensions)\n"}, {"text": "   - Card 18: Not visible (0 dimensions)\n"}, {"text": "   - Card 19: Not visible (0 dimensions)\n"}, {"text": "   - Card 20: Not visible (0 dimensions)\n"}, {"text": "   - Card 21: Not visible (0 dimensions)\n"}, {"text": "   - Card 22: Not visible (0 dimensions)\n"}, {"text": "   - Card 23: Not visible (0 dimensions)\n"}, {"text": "   - Card 24: Not visible (0 dimensions)\n"}, {"text": "   - Card 25: Not visible (0 dimensions)\n"}, {"text": "   - Card 26: Not visible (0 dimensions)\n"}, {"text": "   - Card 27: Not visible (0 dimensions)\n"}, {"text": "   - Card 28: Not visible (0 dimensions)\n"}, {"text": "   - Card 29: Not visible (0 dimensions)\n"}, {"text": "\n--- Horizontal Scrolling Analysis for Activities ---\n"}, {"text": "✓ No unwanted horizontal scrolling detected\n"}, {"text": "Found 1 intentional horizontal scroll containers\n"}], "stderr": [], "retry": 0, "startTime": "2025-06-25T05:56:35.013Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "0f6969d158d38e126276-d797fd39461bb935dd18", "file": "mobile-ux-analysis.spec.ts", "line": 22, "column": 5}, {"title": "Mobile Analysis: Activities Page", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "webkit", "projectName": "webkit", "results": [{"workerIndex": 2, "parallelIndex": 0, "status": "passed", "duration": 9748, "errors": [], "stdout": [{"text": "\n=== ANALYZING ACTIVITIES PAGE ===\n"}, {"text": "✓ Screenshot captured for Activities page\n"}, {"text": "Navigation elements found: 2\n"}, {"text": "\n--- Mobile Issues Analysis for Activities ---\n"}, {"text": "⚠️  Found 15 button pairs with insufficient spacing (<8px)\n"}, {"text": "\n--- Touch Target Analysis for Activities ---\n"}, {"text": "⚠️  Found 34 touch targets smaller than 44px (2025 standard)\n"}, {"text": "\n--- Card Layout Analysis for Activities ---\n"}, {"text": "Found 32 potential card containers\n"}, {"text": "⚠️  Card layout issues found:\n"}, {"text": "   - Card 2: Not visible (0 dimensions)\n"}, {"text": "   - Card 3: Not visible (0 dimensions)\n"}, {"text": "   - Card 4: Not visible (0 dimensions)\n"}, {"text": "   - Card 5: Not visible (0 dimensions)\n"}, {"text": "   - Card 6: Not visible (0 dimensions)\n"}, {"text": "   - Card 7: Not visible (0 dimensions)\n"}, {"text": "   - Card 8: Not visible (0 dimensions)\n"}, {"text": "   - Card 9: Not visible (0 dimensions)\n"}, {"text": "   - Card 10: Not visible (0 dimensions)\n"}, {"text": "   - Card 11: Not visible (0 dimensions)\n"}, {"text": "   - Card 12: Not visible (0 dimensions)\n"}, {"text": "   - Card 13: Not visible (0 dimensions)\n"}, {"text": "   - Card 14: Not visible (0 dimensions)\n"}, {"text": "   - Card 15: Not visible (0 dimensions)\n"}, {"text": "   - Card 16: Not visible (0 dimensions)\n"}, {"text": "   - Card 17: Not visible (0 dimensions)\n"}, {"text": "   - Card 18: Not visible (0 dimensions)\n"}, {"text": "   - Card 19: Not visible (0 dimensions)\n"}, {"text": "   - Card 20: Not visible (0 dimensions)\n"}, {"text": "   - Card 21: Not visible (0 dimensions)\n"}, {"text": "   - Card 22: Not visible (0 dimensions)\n"}, {"text": "   - Card 23: Not visible (0 dimensions)\n"}, {"text": "   - Card 24: Not visible (0 dimensions)\n"}, {"text": "   - Card 25: Not visible (0 dimensions)\n"}, {"text": "   - Card 26: Not visible (0 dimensions)\n"}, {"text": "   - Card 27: Not visible (0 dimensions)\n"}, {"text": "   - Card 28: Not visible (0 dimensions)\n"}, {"text": "   - Card 29: Not visible (0 dimensions)\n"}, {"text": "\n--- Horizontal Scrolling Analysis for Activities ---\n"}, {"text": "✓ No unwanted horizontal scrolling detected\n"}, {"text": "Found 1 intentional horizontal scroll containers\n"}], "stderr": [], "retry": 0, "startTime": "2025-06-25T05:56:58.615Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "0f6969d158d38e126276-a114aa79f7500cf16204", "file": "mobile-ux-analysis.spec.ts", "line": 22, "column": 5}, {"title": "Mobile Analysis: Activities Page", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Chrome", "projectName": "Mobile Chrome", "results": [{"workerIndex": 4, "parallelIndex": 1, "status": "passed", "duration": 10281, "errors": [], "stdout": [{"text": "\n=== ANALYZING ACTIVITIES PAGE ===\n"}, {"text": "✓ Screenshot captured for Activities page\n"}, {"text": "Navigation elements found: 2\n"}, {"text": "\n--- Mobile Issues Analysis for Activities ---\n"}, {"text": "\n--- Touch Target Analysis for Activities ---\n"}, {"text": "⚠️  Found 18 touch targets smaller than 44px (2025 standard)\n"}, {"text": "\n--- Card Layout Analysis for Activities ---\n"}, {"text": "Found 17 potential card containers\n"}, {"text": "✓ Card layouts appear mobile-friendly\n"}, {"text": "\n--- Horizontal Scrolling Analysis for Activities ---\n"}, {"text": "✓ No unwanted horizontal scrolling detected\n"}], "stderr": [], "retry": 0, "startTime": "2025-06-25T05:57:32.890Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "0f6969d158d38e126276-37f0270e595bfde8ff78", "file": "mobile-ux-analysis.spec.ts", "line": 22, "column": 5}, {"title": "Mobile Analysis: Activities Page", "ok": false, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Safari", "projectName": "Mobile Safari", "results": [{"workerIndex": 3, "parallelIndex": 0, "status": "failed", "duration": 13982, "error": {"message": "Error: page.screenshot: Cannot take screenshot larger than 32767 pixels on any dimension\nCall log:\n\u001b[2m  - taking page screenshot\u001b[22m\n\u001b[2m  - waiting for fonts to load...\u001b[22m\n\u001b[2m  - fonts loaded\u001b[22m\n", "stack": "Error: page.screenshot: Cannot take screenshot larger than 32767 pixels on any dimension\nCall log:\n\u001b[2m  - taking page screenshot\u001b[22m\n\u001b[2m  - waiting for fonts to load...\u001b[22m\n\u001b[2m  - fonts loaded\u001b[22m\n\n    at E:\\Augment Code Testing\\sri-lanka-tourist-guide\\tests\\mobile-ux-analysis.spec.ts:34:18", "location": {"file": "E:\\Augment Code Testing\\sri-lanka-tourist-guide\\tests\\mobile-ux-analysis.spec.ts", "column": 18, "line": 34}, "snippet": "\u001b[0m \u001b[90m 32 |\u001b[39m       \u001b[90m// Take full-page screenshot\u001b[39m\n \u001b[90m 33 |\u001b[39m       \u001b[36mconst\u001b[39m timestamp \u001b[33m=\u001b[39m \u001b[36mnew\u001b[39m \u001b[33mDate\u001b[39m()\u001b[33m.\u001b[39mtoISOString()\u001b[33m.\u001b[39mreplace(\u001b[35m/[:.]/g\u001b[39m\u001b[33m,\u001b[39m \u001b[32m'-'\u001b[39m)\u001b[33m;\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 34 |\u001b[39m       \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mscreenshot({ \n \u001b[90m    |\u001b[39m                  \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 35 |\u001b[39m         path\u001b[33m:\u001b[39m \u001b[32m`test-results/mobile-${pageInfo.name.toLowerCase()}-${timestamp}.png`\u001b[39m\u001b[33m,\u001b[39m\n \u001b[90m 36 |\u001b[39m         fullPage\u001b[33m:\u001b[39m \u001b[36mtrue\u001b[39m \n \u001b[90m 37 |\u001b[39m       })\u001b[33m;\u001b[39m\u001b[0m"}, "errors": [{"location": {"file": "E:\\Augment Code Testing\\sri-lanka-tourist-guide\\tests\\mobile-ux-analysis.spec.ts", "column": 18, "line": 34}, "message": "Error: page.screenshot: Cannot take screenshot larger than 32767 pixels on any dimension\nCall log:\n\u001b[2m  - taking page screenshot\u001b[22m\n\u001b[2m  - waiting for fonts to load...\u001b[22m\n\u001b[2m  - fonts loaded\u001b[22m\n\n\n\u001b[0m \u001b[90m 32 |\u001b[39m       \u001b[90m// Take full-page screenshot\u001b[39m\n \u001b[90m 33 |\u001b[39m       \u001b[36mconst\u001b[39m timestamp \u001b[33m=\u001b[39m \u001b[36mnew\u001b[39m \u001b[33mDate\u001b[39m()\u001b[33m.\u001b[39mtoISOString()\u001b[33m.\u001b[39mreplace(\u001b[35m/[:.]/g\u001b[39m\u001b[33m,\u001b[39m \u001b[32m'-'\u001b[39m)\u001b[33m;\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 34 |\u001b[39m       \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mscreenshot({ \n \u001b[90m    |\u001b[39m                  \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 35 |\u001b[39m         path\u001b[33m:\u001b[39m \u001b[32m`test-results/mobile-${pageInfo.name.toLowerCase()}-${timestamp}.png`\u001b[39m\u001b[33m,\u001b[39m\n \u001b[90m 36 |\u001b[39m         fullPage\u001b[33m:\u001b[39m \u001b[36mtrue\u001b[39m \n \u001b[90m 37 |\u001b[39m       })\u001b[33m;\u001b[39m\u001b[0m\n\u001b[2m    at E:\\Augment Code Testing\\sri-lanka-tourist-guide\\tests\\mobile-ux-analysis.spec.ts:34:18\u001b[22m"}], "stdout": [{"text": "\n=== ANALYZING ACTIVITIES PAGE ===\n"}], "stderr": [], "retry": 0, "startTime": "2025-06-25T05:57:13.725Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "E:\\Augment Code Testing\\sri-lanka-tourist-guide\\test-results\\mobile-ux-analysis-Mobile--b341b-le-Analysis-Activities-Page-Mobile-Safari\\test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "E:\\Augment Code Testing\\sri-lanka-tourist-guide\\test-results\\mobile-ux-analysis-Mobile--b341b-le-Analysis-Activities-Page-Mobile-Safari\\video.webm"}, {"name": "error-context", "contentType": "text/markdown", "path": "E:\\Augment Code Testing\\sri-lanka-tourist-guide\\test-results\\mobile-ux-analysis-Mobile--b341b-le-Analysis-Activities-Page-Mobile-Safari\\error-context.md"}], "errorLocation": {"file": "E:\\Augment Code Testing\\sri-lanka-tourist-guide\\tests\\mobile-ux-analysis.spec.ts", "column": 18, "line": 34}}], "status": "unexpected"}], "id": "0f6969d158d38e126276-ee90f1df75ff1f308624", "file": "mobile-ux-analysis.spec.ts", "line": 22, "column": 5}]}]}], "errors": [], "stats": {"startTime": "2025-06-25T05:56:32.517Z", "duration": 72081.906, "expected": 4, "skipped": 0, "unexpected": 1, "flaky": 0}}