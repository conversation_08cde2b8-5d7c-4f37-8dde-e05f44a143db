{"config": {"configFile": "E:\\Augment Code Testing\\sri-lanka-tourist-guide\\playwright.config.ts", "rootDir": "E:/Augment Code Testing/sri-lanka-tourist-guide/tests", "forbidOnly": false, "fullyParallel": true, "globalSetup": null, "globalTeardown": null, "globalTimeout": 0, "grep": {}, "grepInvert": null, "maxFailures": 0, "metadata": {"actualWorkers": 2}, "preserveOutput": "always", "reporter": [["html", null], ["json", {"outputFile": "test-results/results.json"}], ["junit", {"outputFile": "test-results/results.xml"}]], "reportSlowTests": {"max": 5, "threshold": 300000}, "quiet": false, "projects": [{"outputDir": "E:/Augment Code Testing/sri-lanka-tourist-guide/test-results", "repeatEach": 1, "retries": 0, "metadata": {"actualWorkers": 2}, "id": "chromium", "name": "chromium", "testDir": "E:/Augment Code Testing/sri-lanka-tourist-guide/tests", "testIgnore": [], "testMatch": ["**/*.@(spec|test).?(c|m)[jt]s?(x)"], "timeout": 30000}, {"outputDir": "E:/Augment Code Testing/sri-lanka-tourist-guide/test-results", "repeatEach": 1, "retries": 0, "metadata": {"actualWorkers": 2}, "id": "firefox", "name": "firefox", "testDir": "E:/Augment Code Testing/sri-lanka-tourist-guide/tests", "testIgnore": [], "testMatch": ["**/*.@(spec|test).?(c|m)[jt]s?(x)"], "timeout": 30000}, {"outputDir": "E:/Augment Code Testing/sri-lanka-tourist-guide/test-results", "repeatEach": 1, "retries": 0, "metadata": {"actualWorkers": 2}, "id": "webkit", "name": "webkit", "testDir": "E:/Augment Code Testing/sri-lanka-tourist-guide/tests", "testIgnore": [], "testMatch": ["**/*.@(spec|test).?(c|m)[jt]s?(x)"], "timeout": 30000}, {"outputDir": "E:/Augment Code Testing/sri-lanka-tourist-guide/test-results", "repeatEach": 1, "retries": 0, "metadata": {"actualWorkers": 2}, "id": "Mobile Chrome", "name": "Mobile Chrome", "testDir": "E:/Augment Code Testing/sri-lanka-tourist-guide/tests", "testIgnore": [], "testMatch": ["**/*.@(spec|test).?(c|m)[jt]s?(x)"], "timeout": 30000}, {"outputDir": "E:/Augment Code Testing/sri-lanka-tourist-guide/test-results", "repeatEach": 1, "retries": 0, "metadata": {"actualWorkers": 2}, "id": "Mobile Safari", "name": "Mobile Safari", "testDir": "E:/Augment Code Testing/sri-lanka-tourist-guide/tests", "testIgnore": [], "testMatch": ["**/*.@(spec|test).?(c|m)[jt]s?(x)"], "timeout": 30000}], "shard": null, "updateSnapshots": "missing", "updateSourceMethod": "patch", "version": "1.53.1", "workers": 2, "webServer": {"command": "npm run dev", "url": "http://localhost:5173", "reuseExistingServer": true, "timeout": 120000}}, "suites": [{"title": "systematic-page-analysis.spec.ts", "file": "systematic-page-analysis.spec.ts", "column": 0, "line": 0, "specs": [], "suites": [{"title": "Systematic Page Analysis - 2025 Best Practices", "file": "systematic-page-analysis.spec.ts", "line": 3, "column": 6, "specs": [{"title": "Activities Page - Desktop and Mobile Screenshots", "ok": false, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 0, "parallelIndex": 0, "status": "failed", "duration": 6901, "error": {"message": "Error: page.goto: net::ERR_CONNECTION_REFUSED at http://localhost:5174/activities\nCall log:\n\u001b[2m  - navigating to \"http://localhost:5174/activities\", waiting until \"load\"\u001b[22m\n", "stack": "Error: page.goto: net::ERR_CONNECTION_REFUSED at http://localhost:5174/activities\nCall log:\n\u001b[2m  - navigating to \"http://localhost:5174/activities\", waiting until \"load\"\u001b[22m\n\n    at E:\\Augment Code Testing\\sri-lanka-tourist-guide\\tests\\systematic-page-analysis.spec.ts:9:16", "location": {"file": "E:\\Augment Code Testing\\sri-lanka-tourist-guide\\tests\\systematic-page-analysis.spec.ts", "column": 16, "line": 9}, "snippet": "\u001b[0m \u001b[90m  7 |\u001b[39m     \u001b[90m// Desktop screenshot\u001b[39m\n \u001b[90m  8 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39msetViewportSize({ width\u001b[33m:\u001b[39m \u001b[35m1200\u001b[39m\u001b[33m,\u001b[39m height\u001b[33m:\u001b[39m \u001b[35m800\u001b[39m })\u001b[33m;\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m  9 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mgoto(\u001b[32m'http://localhost:5174/activities'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m    |\u001b[39m                \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 10 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForLoadState(\u001b[32m'networkidle'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m 11 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForTimeout(\u001b[35m2000\u001b[39m)\u001b[33m;\u001b[39m \u001b[90m// Wait for any animations\u001b[39m\n \u001b[90m 12 |\u001b[39m     \u001b[0m"}, "errors": [{"location": {"file": "E:\\Augment Code Testing\\sri-lanka-tourist-guide\\tests\\systematic-page-analysis.spec.ts", "column": 16, "line": 9}, "message": "Error: page.goto: net::ERR_CONNECTION_REFUSED at http://localhost:5174/activities\nCall log:\n\u001b[2m  - navigating to \"http://localhost:5174/activities\", waiting until \"load\"\u001b[22m\n\n\n\u001b[0m \u001b[90m  7 |\u001b[39m     \u001b[90m// Desktop screenshot\u001b[39m\n \u001b[90m  8 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39msetViewportSize({ width\u001b[33m:\u001b[39m \u001b[35m1200\u001b[39m\u001b[33m,\u001b[39m height\u001b[33m:\u001b[39m \u001b[35m800\u001b[39m })\u001b[33m;\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m  9 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mgoto(\u001b[32m'http://localhost:5174/activities'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m    |\u001b[39m                \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 10 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForLoadState(\u001b[32m'networkidle'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m 11 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForTimeout(\u001b[35m2000\u001b[39m)\u001b[33m;\u001b[39m \u001b[90m// Wait for any animations\u001b[39m\n \u001b[90m 12 |\u001b[39m     \u001b[0m\n\u001b[2m    at E:\\Augment Code Testing\\sri-lanka-tourist-guide\\tests\\systematic-page-analysis.spec.ts:9:16\u001b[22m"}], "stdout": [{"text": "📸 Taking Activities page screenshots for analysis...\n"}], "stderr": [], "retry": 0, "startTime": "2025-06-25T04:56:23.665Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "E:\\Augment Code Testing\\sri-lanka-tourist-guide\\test-results\\systematic-page-analysis-S-2637b-ktop-and-Mobile-Screenshots-chromium\\test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "E:\\Augment Code Testing\\sri-lanka-tourist-guide\\test-results\\systematic-page-analysis-S-2637b-ktop-and-Mobile-Screenshots-chromium\\video.webm"}, {"name": "error-context", "contentType": "text/markdown", "path": "E:\\Augment Code Testing\\sri-lanka-tourist-guide\\test-results\\systematic-page-analysis-S-2637b-ktop-and-Mobile-Screenshots-chromium\\error-context.md"}], "errorLocation": {"file": "E:\\Augment Code Testing\\sri-lanka-tourist-guide\\tests\\systematic-page-analysis.spec.ts", "column": 16, "line": 9}}], "status": "unexpected"}], "id": "6eb218e0fdf0c7411726-7ec1ac4576dd2d8fcd52", "file": "systematic-page-analysis.spec.ts", "line": 4, "column": 3}, {"title": "Activities Page - Desktop and Mobile Screenshots", "ok": false, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "firefox", "projectName": "firefox", "results": [{"workerIndex": 1, "parallelIndex": 1, "status": "failed", "duration": 6881, "error": {"message": "Error: page.goto: NS_ERROR_CONNECTION_REFUSED\nCall log:\n\u001b[2m  - navigating to \"http://localhost:5174/activities\", waiting until \"load\"\u001b[22m\n", "stack": "Error: page.goto: NS_ERROR_CONNECTION_REFUSED\nCall log:\n\u001b[2m  - navigating to \"http://localhost:5174/activities\", waiting until \"load\"\u001b[22m\n\n    at E:\\Augment Code Testing\\sri-lanka-tourist-guide\\tests\\systematic-page-analysis.spec.ts:9:16", "location": {"file": "E:\\Augment Code Testing\\sri-lanka-tourist-guide\\tests\\systematic-page-analysis.spec.ts", "column": 16, "line": 9}, "snippet": "\u001b[0m \u001b[90m  7 |\u001b[39m     \u001b[90m// Desktop screenshot\u001b[39m\n \u001b[90m  8 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39msetViewportSize({ width\u001b[33m:\u001b[39m \u001b[35m1200\u001b[39m\u001b[33m,\u001b[39m height\u001b[33m:\u001b[39m \u001b[35m800\u001b[39m })\u001b[33m;\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m  9 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mgoto(\u001b[32m'http://localhost:5174/activities'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m    |\u001b[39m                \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 10 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForLoadState(\u001b[32m'networkidle'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m 11 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForTimeout(\u001b[35m2000\u001b[39m)\u001b[33m;\u001b[39m \u001b[90m// Wait for any animations\u001b[39m\n \u001b[90m 12 |\u001b[39m     \u001b[0m"}, "errors": [{"location": {"file": "E:\\Augment Code Testing\\sri-lanka-tourist-guide\\tests\\systematic-page-analysis.spec.ts", "column": 16, "line": 9}, "message": "Error: page.goto: NS_ERROR_CONNECTION_REFUSED\nCall log:\n\u001b[2m  - navigating to \"http://localhost:5174/activities\", waiting until \"load\"\u001b[22m\n\n\n\u001b[0m \u001b[90m  7 |\u001b[39m     \u001b[90m// Desktop screenshot\u001b[39m\n \u001b[90m  8 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39msetViewportSize({ width\u001b[33m:\u001b[39m \u001b[35m1200\u001b[39m\u001b[33m,\u001b[39m height\u001b[33m:\u001b[39m \u001b[35m800\u001b[39m })\u001b[33m;\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m  9 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mgoto(\u001b[32m'http://localhost:5174/activities'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m    |\u001b[39m                \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 10 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForLoadState(\u001b[32m'networkidle'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m 11 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForTimeout(\u001b[35m2000\u001b[39m)\u001b[33m;\u001b[39m \u001b[90m// Wait for any animations\u001b[39m\n \u001b[90m 12 |\u001b[39m     \u001b[0m\n\u001b[2m    at E:\\Augment Code Testing\\sri-lanka-tourist-guide\\tests\\systematic-page-analysis.spec.ts:9:16\u001b[22m"}], "stdout": [{"text": "📸 Taking Activities page screenshots for analysis...\n"}], "stderr": [], "retry": 0, "startTime": "2025-06-25T04:56:23.789Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "E:\\Augment Code Testing\\sri-lanka-tourist-guide\\test-results\\systematic-page-analysis-S-2637b-ktop-and-Mobile-Screenshots-firefox\\test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "E:\\Augment Code Testing\\sri-lanka-tourist-guide\\test-results\\systematic-page-analysis-S-2637b-ktop-and-Mobile-Screenshots-firefox\\video.webm"}, {"name": "error-context", "contentType": "text/markdown", "path": "E:\\Augment Code Testing\\sri-lanka-tourist-guide\\test-results\\systematic-page-analysis-S-2637b-ktop-and-Mobile-Screenshots-firefox\\error-context.md"}], "errorLocation": {"file": "E:\\Augment Code Testing\\sri-lanka-tourist-guide\\tests\\systematic-page-analysis.spec.ts", "column": 16, "line": 9}}], "status": "unexpected"}], "id": "6eb218e0fdf0c7411726-a6d7dff24274d5026b28", "file": "systematic-page-analysis.spec.ts", "line": 4, "column": 3}, {"title": "Activities Page - Desktop and Mobile Screenshots", "ok": false, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "webkit", "projectName": "webkit", "results": [{"workerIndex": 2, "parallelIndex": 0, "status": "failed", "duration": 4532, "error": {"message": "Error: page.goto: Could not connect to server\nCall log:\n\u001b[2m  - navigating to \"http://localhost:5174/activities\", waiting until \"load\"\u001b[22m\n", "stack": "Error: page.goto: Could not connect to server\nCall log:\n\u001b[2m  - navigating to \"http://localhost:5174/activities\", waiting until \"load\"\u001b[22m\n\n    at E:\\Augment Code Testing\\sri-lanka-tourist-guide\\tests\\systematic-page-analysis.spec.ts:9:16", "location": {"file": "E:\\Augment Code Testing\\sri-lanka-tourist-guide\\tests\\systematic-page-analysis.spec.ts", "column": 16, "line": 9}, "snippet": "\u001b[0m \u001b[90m  7 |\u001b[39m     \u001b[90m// Desktop screenshot\u001b[39m\n \u001b[90m  8 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39msetViewportSize({ width\u001b[33m:\u001b[39m \u001b[35m1200\u001b[39m\u001b[33m,\u001b[39m height\u001b[33m:\u001b[39m \u001b[35m800\u001b[39m })\u001b[33m;\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m  9 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mgoto(\u001b[32m'http://localhost:5174/activities'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m    |\u001b[39m                \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 10 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForLoadState(\u001b[32m'networkidle'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m 11 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForTimeout(\u001b[35m2000\u001b[39m)\u001b[33m;\u001b[39m \u001b[90m// Wait for any animations\u001b[39m\n \u001b[90m 12 |\u001b[39m     \u001b[0m"}, "errors": [{"location": {"file": "E:\\Augment Code Testing\\sri-lanka-tourist-guide\\tests\\systematic-page-analysis.spec.ts", "column": 16, "line": 9}, "message": "Error: page.goto: Could not connect to server\nCall log:\n\u001b[2m  - navigating to \"http://localhost:5174/activities\", waiting until \"load\"\u001b[22m\n\n\n\u001b[0m \u001b[90m  7 |\u001b[39m     \u001b[90m// Desktop screenshot\u001b[39m\n \u001b[90m  8 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39msetViewportSize({ width\u001b[33m:\u001b[39m \u001b[35m1200\u001b[39m\u001b[33m,\u001b[39m height\u001b[33m:\u001b[39m \u001b[35m800\u001b[39m })\u001b[33m;\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m  9 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mgoto(\u001b[32m'http://localhost:5174/activities'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m    |\u001b[39m                \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 10 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForLoadState(\u001b[32m'networkidle'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m 11 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForTimeout(\u001b[35m2000\u001b[39m)\u001b[33m;\u001b[39m \u001b[90m// Wait for any animations\u001b[39m\n \u001b[90m 12 |\u001b[39m     \u001b[0m\n\u001b[2m    at E:\\Augment Code Testing\\sri-lanka-tourist-guide\\tests\\systematic-page-analysis.spec.ts:9:16\u001b[22m"}], "stdout": [{"text": "📸 Taking Activities page screenshots for analysis...\n"}], "stderr": [], "retry": 0, "startTime": "2025-06-25T04:56:35.241Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "E:\\Augment Code Testing\\sri-lanka-tourist-guide\\test-results\\systematic-page-analysis-S-2637b-ktop-and-Mobile-Screenshots-webkit\\test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "E:\\Augment Code Testing\\sri-lanka-tourist-guide\\test-results\\systematic-page-analysis-S-2637b-ktop-and-Mobile-Screenshots-webkit\\video.webm"}], "errorLocation": {"file": "E:\\Augment Code Testing\\sri-lanka-tourist-guide\\tests\\systematic-page-analysis.spec.ts", "column": 16, "line": 9}}], "status": "unexpected"}], "id": "6eb218e0fdf0c7411726-617374cbf42937247dbd", "file": "systematic-page-analysis.spec.ts", "line": 4, "column": 3}, {"title": "Activities Page - Desktop and Mobile Screenshots", "ok": false, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Chrome", "projectName": "Mobile Chrome", "results": [{"workerIndex": 3, "parallelIndex": 1, "status": "failed", "duration": 4186, "error": {"message": "Error: page.goto: net::ERR_CONNECTION_REFUSED at http://localhost:5174/activities\nCall log:\n\u001b[2m  - navigating to \"http://localhost:5174/activities\", waiting until \"load\"\u001b[22m\n", "stack": "Error: page.goto: net::ERR_CONNECTION_REFUSED at http://localhost:5174/activities\nCall log:\n\u001b[2m  - navigating to \"http://localhost:5174/activities\", waiting until \"load\"\u001b[22m\n\n    at E:\\Augment Code Testing\\sri-lanka-tourist-guide\\tests\\systematic-page-analysis.spec.ts:9:16", "location": {"file": "E:\\Augment Code Testing\\sri-lanka-tourist-guide\\tests\\systematic-page-analysis.spec.ts", "column": 16, "line": 9}, "snippet": "\u001b[0m \u001b[90m  7 |\u001b[39m     \u001b[90m// Desktop screenshot\u001b[39m\n \u001b[90m  8 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39msetViewportSize({ width\u001b[33m:\u001b[39m \u001b[35m1200\u001b[39m\u001b[33m,\u001b[39m height\u001b[33m:\u001b[39m \u001b[35m800\u001b[39m })\u001b[33m;\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m  9 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mgoto(\u001b[32m'http://localhost:5174/activities'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m    |\u001b[39m                \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 10 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForLoadState(\u001b[32m'networkidle'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m 11 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForTimeout(\u001b[35m2000\u001b[39m)\u001b[33m;\u001b[39m \u001b[90m// Wait for any animations\u001b[39m\n \u001b[90m 12 |\u001b[39m     \u001b[0m"}, "errors": [{"location": {"file": "E:\\Augment Code Testing\\sri-lanka-tourist-guide\\tests\\systematic-page-analysis.spec.ts", "column": 16, "line": 9}, "message": "Error: page.goto: net::ERR_CONNECTION_REFUSED at http://localhost:5174/activities\nCall log:\n\u001b[2m  - navigating to \"http://localhost:5174/activities\", waiting until \"load\"\u001b[22m\n\n\n\u001b[0m \u001b[90m  7 |\u001b[39m     \u001b[90m// Desktop screenshot\u001b[39m\n \u001b[90m  8 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39msetViewportSize({ width\u001b[33m:\u001b[39m \u001b[35m1200\u001b[39m\u001b[33m,\u001b[39m height\u001b[33m:\u001b[39m \u001b[35m800\u001b[39m })\u001b[33m;\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m  9 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mgoto(\u001b[32m'http://localhost:5174/activities'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m    |\u001b[39m                \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 10 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForLoadState(\u001b[32m'networkidle'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m 11 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForTimeout(\u001b[35m2000\u001b[39m)\u001b[33m;\u001b[39m \u001b[90m// Wait for any animations\u001b[39m\n \u001b[90m 12 |\u001b[39m     \u001b[0m\n\u001b[2m    at E:\\Augment Code Testing\\sri-lanka-tourist-guide\\tests\\systematic-page-analysis.spec.ts:9:16\u001b[22m"}], "stdout": [{"text": "📸 Taking Activities page screenshots for analysis...\n"}], "stderr": [], "retry": 0, "startTime": "2025-06-25T04:56:38.062Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "E:\\Augment Code Testing\\sri-lanka-tourist-guide\\test-results\\systematic-page-analysis-S-2637b-ktop-and-Mobile-Screenshots-Mobile-Chrome\\test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "E:\\Augment Code Testing\\sri-lanka-tourist-guide\\test-results\\systematic-page-analysis-S-2637b-ktop-and-Mobile-Screenshots-Mobile-Chrome\\video.webm"}, {"name": "error-context", "contentType": "text/markdown", "path": "E:\\Augment Code Testing\\sri-lanka-tourist-guide\\test-results\\systematic-page-analysis-S-2637b-ktop-and-Mobile-Screenshots-Mobile-Chrome\\error-context.md"}], "errorLocation": {"file": "E:\\Augment Code Testing\\sri-lanka-tourist-guide\\tests\\systematic-page-analysis.spec.ts", "column": 16, "line": 9}}], "status": "unexpected"}], "id": "6eb218e0fdf0c7411726-b3418ab3c494daeda370", "file": "systematic-page-analysis.spec.ts", "line": 4, "column": 3}, {"title": "Activities Page - Desktop and Mobile Screenshots", "ok": false, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Safari", "projectName": "Mobile Safari", "results": [{"workerIndex": 4, "parallelIndex": 0, "status": "failed", "duration": 5755, "error": {"message": "Error: page.goto: Could not connect to server\nCall log:\n\u001b[2m  - navigating to \"http://localhost:5174/activities\", waiting until \"load\"\u001b[22m\n", "stack": "Error: page.goto: Could not connect to server\nCall log:\n\u001b[2m  - navigating to \"http://localhost:5174/activities\", waiting until \"load\"\u001b[22m\n\n    at E:\\Augment Code Testing\\sri-lanka-tourist-guide\\tests\\systematic-page-analysis.spec.ts:9:16", "location": {"file": "E:\\Augment Code Testing\\sri-lanka-tourist-guide\\tests\\systematic-page-analysis.spec.ts", "column": 16, "line": 9}, "snippet": "\u001b[0m \u001b[90m  7 |\u001b[39m     \u001b[90m// Desktop screenshot\u001b[39m\n \u001b[90m  8 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39msetViewportSize({ width\u001b[33m:\u001b[39m \u001b[35m1200\u001b[39m\u001b[33m,\u001b[39m height\u001b[33m:\u001b[39m \u001b[35m800\u001b[39m })\u001b[33m;\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m  9 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mgoto(\u001b[32m'http://localhost:5174/activities'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m    |\u001b[39m                \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 10 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForLoadState(\u001b[32m'networkidle'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m 11 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForTimeout(\u001b[35m2000\u001b[39m)\u001b[33m;\u001b[39m \u001b[90m// Wait for any animations\u001b[39m\n \u001b[90m 12 |\u001b[39m     \u001b[0m"}, "errors": [{"location": {"file": "E:\\Augment Code Testing\\sri-lanka-tourist-guide\\tests\\systematic-page-analysis.spec.ts", "column": 16, "line": 9}, "message": "Error: page.goto: Could not connect to server\nCall log:\n\u001b[2m  - navigating to \"http://localhost:5174/activities\", waiting until \"load\"\u001b[22m\n\n\n\u001b[0m \u001b[90m  7 |\u001b[39m     \u001b[90m// Desktop screenshot\u001b[39m\n \u001b[90m  8 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39msetViewportSize({ width\u001b[33m:\u001b[39m \u001b[35m1200\u001b[39m\u001b[33m,\u001b[39m height\u001b[33m:\u001b[39m \u001b[35m800\u001b[39m })\u001b[33m;\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m  9 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mgoto(\u001b[32m'http://localhost:5174/activities'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m    |\u001b[39m                \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 10 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForLoadState(\u001b[32m'networkidle'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m 11 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForTimeout(\u001b[35m2000\u001b[39m)\u001b[33m;\u001b[39m \u001b[90m// Wait for any animations\u001b[39m\n \u001b[90m 12 |\u001b[39m     \u001b[0m\n\u001b[2m    at E:\\Augment Code Testing\\sri-lanka-tourist-guide\\tests\\systematic-page-analysis.spec.ts:9:16\u001b[22m"}], "stdout": [{"text": "📸 Taking Activities page screenshots for analysis...\n"}], "stderr": [], "retry": 0, "startTime": "2025-06-25T04:56:43.093Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "E:\\Augment Code Testing\\sri-lanka-tourist-guide\\test-results\\systematic-page-analysis-S-2637b-ktop-and-Mobile-Screenshots-Mobile-Safari\\test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "E:\\Augment Code Testing\\sri-lanka-tourist-guide\\test-results\\systematic-page-analysis-S-2637b-ktop-and-Mobile-Screenshots-Mobile-Safari\\video.webm"}], "errorLocation": {"file": "E:\\Augment Code Testing\\sri-lanka-tourist-guide\\tests\\systematic-page-analysis.spec.ts", "column": 16, "line": 9}}], "status": "unexpected"}], "id": "6eb218e0fdf0c7411726-ac79da24448782885f9f", "file": "systematic-page-analysis.spec.ts", "line": 4, "column": 3}]}]}], "errors": [], "stats": {"startTime": "2025-06-25T04:56:20.824Z", "duration": 28309.************, "expected": 0, "skipped": 0, "unexpected": 5, "flaky": 0}}