{"name": "sri-lanka-tourist-guide", "private": true, "version": "0.1.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc && vite build", "build:production": "node scripts/build-production.js", "build:analyze": "npm run build && npx vite-bundle-analyzer dist", "lint": "eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "lint:fix": "eslint . --ext ts,tsx --fix", "preview": "vite preview", "type-check": "tsc --noEmit", "test": "playwright test", "test:ui": "playwright test --ui", "test:headed": "playwright test --headed", "test:debug": "playwright test --debug", "test:ci": "playwright test --reporter=html", "generate:sitemap": "node scripts/generateSitemap.js", "generate:content": "node scripts/integrateContent.js", "optimize:images": "echo 'Image optimization would run here'", "deploy:staging": "npm run build:production && echo 'Deploy to staging'", "deploy:production": "npm run build:production && echo 'Deploy to production'", "fix:critical": "node scripts/fixCriticalIssues.js", "test:comprehensive": "node scripts/runComprehensiveTests.js", "test:report": "npm run test:comprehensive && echo 'Test report generated'"}, "dependencies": {"@gsap/react": "^2.1.2", "@headlessui/react": "^2.2.4", "@heroicons/react": "^2.2.0", "@hookform/resolvers": "^5.1.1", "clsx": "^2.1.1", "date-fns": "^4.1.0", "framer-motion": "^12.18.1", "gsap": "^3.13.0", "leaflet": "^1.9.4", "lottie-react": "^2.4.1", "lucide-react": "^0.523.0", "react": "^18.3.1", "react-dom": "^18.3.1", "react-helmet-async": "^2.0.5", "react-hook-form": "^7.58.1", "react-leaflet": "^4.2.1", "react-markdown": "^10.1.0", "react-router-dom": "^6.28.1", "zod": "^3.25.67"}, "devDependencies": {"@playwright/test": "^1.53.1", "@types/dompurify": "^3.0.5", "@types/leaflet": "^1.9.18", "@types/react": "^18.3.12", "@types/react-dom": "^18.3.1", "@typescript-eslint/eslint-plugin": "^8.15.0", "@typescript-eslint/parser": "^8.15.0", "@vitejs/plugin-react": "^4.3.4", "autoprefixer": "^10.4.20", "dompurify": "^3.2.6", "eslint": "^9.15.0", "eslint-plugin-react-hooks": "^5.0.0", "eslint-plugin-react-refresh": "^0.4.14", "marked": "^15.0.12", "postcss": "^8.5.1", "prettier": "^3.5.3", "prettier-plugin-tailwindcss": "^0.6.13", "tailwindcss": "^3.4.15", "terser": "^5.43.1", "typescript": "^5.6.3", "vite": "^6.0.1"}}