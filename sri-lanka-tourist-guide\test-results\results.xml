<testsuites id="" name="" tests="5" failures="1" skipped="0" errors="0" time="72.081906">
<testsuite name="mobile-ux-analysis.spec.ts" timestamp="2025-06-25T05:56:32.698Z" hostname="chromium" tests="1" failures="0" skipped="0" time="8.883" errors="0">
<testcase name="Mobile UX Analysis - 2025 Standards › Mobile Analysis: Activities Page" classname="mobile-ux-analysis.spec.ts" time="8.883">
<system-out>
<![CDATA[
=== ANALYZING ACTIVITIES PAGE ===
✓ Screenshot captured for Activities page
Navigation elements found: 2

--- Mobile Issues Analysis for Activities ---
⚠️  Found 15 button pairs with insufficient spacing (<8px)

--- Touch Target Analysis for Activities ---
⚠️  Found 34 touch targets smaller than 44px (2025 standard)

--- Card Layout Analysis for Activities ---
Found 32 potential card containers
⚠️  Card layout issues found:
   - Card 2: Not visible (0 dimensions)
   - Card 3: Not visible (0 dimensions)
   - Card 4: Not visible (0 dimensions)
   - Card 5: Not visible (0 dimensions)
   - Card 6: Not visible (0 dimensions)
   - Card 7: Not visible (0 dimensions)
   - Card 8: Not visible (0 dimensions)
   - Card 9: Not visible (0 dimensions)
   - Card 10: Not visible (0 dimensions)
   - Card 11: Not visible (0 dimensions)
   - Card 12: Not visible (0 dimensions)
   - Card 13: Not visible (0 dimensions)
   - Card 14: Not visible (0 dimensions)
   - Card 15: Not visible (0 dimensions)
   - Card 16: Not visible (0 dimensions)
   - Card 17: Not visible (0 dimensions)
   - Card 18: Not visible (0 dimensions)
   - Card 19: Not visible (0 dimensions)
   - Card 20: Not visible (0 dimensions)
   - Card 21: Not visible (0 dimensions)
   - Card 22: Not visible (0 dimensions)
   - Card 23: Not visible (0 dimensions)
   - Card 24: Not visible (0 dimensions)
   - Card 25: Not visible (0 dimensions)
   - Card 26: Not visible (0 dimensions)
   - Card 27: Not visible (0 dimensions)
   - Card 28: Not visible (0 dimensions)
   - Card 29: Not visible (0 dimensions)

--- Horizontal Scrolling Analysis for Activities ---
✓ No unwanted horizontal scrolling detected
Found 1 intentional horizontal scroll containers
]]>
</system-out>
</testcase>
</testsuite>
<testsuite name="mobile-ux-analysis.spec.ts" timestamp="2025-06-25T05:56:32.698Z" hostname="firefox" tests="1" failures="0" skipped="0" time="14.779" errors="0">
<testcase name="Mobile UX Analysis - 2025 Standards › Mobile Analysis: Activities Page" classname="mobile-ux-analysis.spec.ts" time="14.779">
<system-out>
<![CDATA[
=== ANALYZING ACTIVITIES PAGE ===
✓ Screenshot captured for Activities page
Navigation elements found: 2

--- Mobile Issues Analysis for Activities ---
⚠️  Found 15 button pairs with insufficient spacing (<8px)

--- Touch Target Analysis for Activities ---
⚠️  Found 34 touch targets smaller than 44px (2025 standard)

--- Card Layout Analysis for Activities ---
Found 32 potential card containers
⚠️  Card layout issues found:
   - Card 2: Not visible (0 dimensions)
   - Card 3: Not visible (0 dimensions)
   - Card 4: Not visible (0 dimensions)
   - Card 5: Not visible (0 dimensions)
   - Card 6: Not visible (0 dimensions)
   - Card 7: Not visible (0 dimensions)
   - Card 8: Not visible (0 dimensions)
   - Card 9: Not visible (0 dimensions)
   - Card 10: Not visible (0 dimensions)
   - Card 11: Not visible (0 dimensions)
   - Card 12: Not visible (0 dimensions)
   - Card 13: Not visible (0 dimensions)
   - Card 14: Not visible (0 dimensions)
   - Card 15: Not visible (0 dimensions)
   - Card 16: Not visible (0 dimensions)
   - Card 17: Not visible (0 dimensions)
   - Card 18: Not visible (0 dimensions)
   - Card 19: Not visible (0 dimensions)
   - Card 20: Not visible (0 dimensions)
   - Card 21: Not visible (0 dimensions)
   - Card 22: Not visible (0 dimensions)
   - Card 23: Not visible (0 dimensions)
   - Card 24: Not visible (0 dimensions)
   - Card 25: Not visible (0 dimensions)
   - Card 26: Not visible (0 dimensions)
   - Card 27: Not visible (0 dimensions)
   - Card 28: Not visible (0 dimensions)
   - Card 29: Not visible (0 dimensions)

--- Horizontal Scrolling Analysis for Activities ---
✓ No unwanted horizontal scrolling detected
Found 1 intentional horizontal scroll containers
]]>
</system-out>
</testcase>
</testsuite>
<testsuite name="mobile-ux-analysis.spec.ts" timestamp="2025-06-25T05:56:32.698Z" hostname="webkit" tests="1" failures="0" skipped="0" time="9.748" errors="0">
<testcase name="Mobile UX Analysis - 2025 Standards › Mobile Analysis: Activities Page" classname="mobile-ux-analysis.spec.ts" time="9.748">
<system-out>
<![CDATA[
=== ANALYZING ACTIVITIES PAGE ===
✓ Screenshot captured for Activities page
Navigation elements found: 2

--- Mobile Issues Analysis for Activities ---
⚠️  Found 15 button pairs with insufficient spacing (<8px)

--- Touch Target Analysis for Activities ---
⚠️  Found 34 touch targets smaller than 44px (2025 standard)

--- Card Layout Analysis for Activities ---
Found 32 potential card containers
⚠️  Card layout issues found:
   - Card 2: Not visible (0 dimensions)
   - Card 3: Not visible (0 dimensions)
   - Card 4: Not visible (0 dimensions)
   - Card 5: Not visible (0 dimensions)
   - Card 6: Not visible (0 dimensions)
   - Card 7: Not visible (0 dimensions)
   - Card 8: Not visible (0 dimensions)
   - Card 9: Not visible (0 dimensions)
   - Card 10: Not visible (0 dimensions)
   - Card 11: Not visible (0 dimensions)
   - Card 12: Not visible (0 dimensions)
   - Card 13: Not visible (0 dimensions)
   - Card 14: Not visible (0 dimensions)
   - Card 15: Not visible (0 dimensions)
   - Card 16: Not visible (0 dimensions)
   - Card 17: Not visible (0 dimensions)
   - Card 18: Not visible (0 dimensions)
   - Card 19: Not visible (0 dimensions)
   - Card 20: Not visible (0 dimensions)
   - Card 21: Not visible (0 dimensions)
   - Card 22: Not visible (0 dimensions)
   - Card 23: Not visible (0 dimensions)
   - Card 24: Not visible (0 dimensions)
   - Card 25: Not visible (0 dimensions)
   - Card 26: Not visible (0 dimensions)
   - Card 27: Not visible (0 dimensions)
   - Card 28: Not visible (0 dimensions)
   - Card 29: Not visible (0 dimensions)

--- Horizontal Scrolling Analysis for Activities ---
✓ No unwanted horizontal scrolling detected
Found 1 intentional horizontal scroll containers
]]>
</system-out>
</testcase>
</testsuite>
<testsuite name="mobile-ux-analysis.spec.ts" timestamp="2025-06-25T05:56:32.698Z" hostname="Mobile Chrome" tests="1" failures="0" skipped="0" time="10.281" errors="0">
<testcase name="Mobile UX Analysis - 2025 Standards › Mobile Analysis: Activities Page" classname="mobile-ux-analysis.spec.ts" time="10.281">
<system-out>
<![CDATA[
=== ANALYZING ACTIVITIES PAGE ===
✓ Screenshot captured for Activities page
Navigation elements found: 2

--- Mobile Issues Analysis for Activities ---

--- Touch Target Analysis for Activities ---
⚠️  Found 18 touch targets smaller than 44px (2025 standard)

--- Card Layout Analysis for Activities ---
Found 17 potential card containers
✓ Card layouts appear mobile-friendly

--- Horizontal Scrolling Analysis for Activities ---
✓ No unwanted horizontal scrolling detected
]]>
</system-out>
</testcase>
</testsuite>
<testsuite name="mobile-ux-analysis.spec.ts" timestamp="2025-06-25T05:56:32.698Z" hostname="Mobile Safari" tests="1" failures="1" skipped="0" time="13.982" errors="0">
<testcase name="Mobile UX Analysis - 2025 Standards › Mobile Analysis: Activities Page" classname="mobile-ux-analysis.spec.ts" time="13.982">
<failure message="mobile-ux-analysis.spec.ts:22:5 Mobile Analysis: Activities Page" type="FAILURE">
<![CDATA[  [Mobile Safari] › mobile-ux-analysis.spec.ts:22:5 › Mobile UX Analysis - 2025 Standards › Mobile Analysis: Activities Page 

    Error: page.screenshot: Cannot take screenshot larger than 32767 pixels on any dimension
    Call log:
      - taking page screenshot
      - waiting for fonts to load...
      - fonts loaded


      32 |       // Take full-page screenshot
      33 |       const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
    > 34 |       await page.screenshot({ 
         |                  ^
      35 |         path: `test-results/mobile-${pageInfo.name.toLowerCase()}-${timestamp}.png`,
      36 |         fullPage: true 
      37 |       });
        at E:\Augment Code Testing\sri-lanka-tourist-guide\tests\mobile-ux-analysis.spec.ts:34:18

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    test-results\mobile-ux-analysis-Mobile--b341b-le-Analysis-Activities-Page-Mobile-Safari\test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    test-results\mobile-ux-analysis-Mobile--b341b-le-Analysis-Activities-Page-Mobile-Safari\video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ..\test-results\mobile-ux-analysis-Mobile--b341b-le-Analysis-Activities-Page-Mobile-Safari\error-context.md
]]>
</failure>
<system-out>
<![CDATA[
=== ANALYZING ACTIVITIES PAGE ===

[[ATTACHMENT|mobile-ux-analysis-Mobile--b341b-le-Analysis-Activities-Page-Mobile-Safari\test-failed-1.png]]

[[ATTACHMENT|mobile-ux-analysis-Mobile--b341b-le-Analysis-Activities-Page-Mobile-Safari\video.webm]]

[[ATTACHMENT|mobile-ux-analysis-Mobile--b341b-le-Analysis-Activities-Page-Mobile-Safari\error-context.md]]
]]>
</system-out>
</testcase>
</testsuite>
</testsuites>