<testsuites id="" name="" tests="5" failures="5" skipped="0" errors="0" time="28.309874000000004">
<testsuite name="systematic-page-analysis.spec.ts" timestamp="2025-06-25T04:56:21.460Z" hostname="chromium" tests="1" failures="1" skipped="0" time="6.901" errors="0">
<testcase name="Systematic Page Analysis - 2025 Best Practices › Activities Page - Desktop and Mobile Screenshots" classname="systematic-page-analysis.spec.ts" time="6.901">
<failure message="systematic-page-analysis.spec.ts:4:3 Activities Page - Desktop and Mobile Screenshots" type="FAILURE">
<![CDATA[  [chromium] › systematic-page-analysis.spec.ts:4:3 › Systematic Page Analysis - 2025 Best Practices › Activities Page - Desktop and Mobile Screenshots 

    Error: page.goto: net::ERR_CONNECTION_REFUSED at http://localhost:5174/activities
    Call log:
      - navigating to "http://localhost:5174/activities", waiting until "load"


       7 |     // Desktop screenshot
       8 |     await page.setViewportSize({ width: 1200, height: 800 });
    >  9 |     await page.goto('http://localhost:5174/activities');
         |                ^
      10 |     await page.waitForLoadState('networkidle');
      11 |     await page.waitForTimeout(2000); // Wait for any animations
      12 |     
        at E:\Augment Code Testing\sri-lanka-tourist-guide\tests\systematic-page-analysis.spec.ts:9:16

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    test-results\systematic-page-analysis-S-2637b-ktop-and-Mobile-Screenshots-chromium\test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    test-results\systematic-page-analysis-S-2637b-ktop-and-Mobile-Screenshots-chromium\video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ..\test-results\systematic-page-analysis-S-2637b-ktop-and-Mobile-Screenshots-chromium\error-context.md
]]>
</failure>
<system-out>
<![CDATA[📸 Taking Activities page screenshots for analysis...

[[ATTACHMENT|systematic-page-analysis-S-2637b-ktop-and-Mobile-Screenshots-chromium\test-failed-1.png]]

[[ATTACHMENT|systematic-page-analysis-S-2637b-ktop-and-Mobile-Screenshots-chromium\video.webm]]

[[ATTACHMENT|systematic-page-analysis-S-2637b-ktop-and-Mobile-Screenshots-chromium\error-context.md]]
]]>
</system-out>
</testcase>
</testsuite>
<testsuite name="systematic-page-analysis.spec.ts" timestamp="2025-06-25T04:56:21.460Z" hostname="firefox" tests="1" failures="1" skipped="0" time="6.881" errors="0">
<testcase name="Systematic Page Analysis - 2025 Best Practices › Activities Page - Desktop and Mobile Screenshots" classname="systematic-page-analysis.spec.ts" time="6.881">
<failure message="systematic-page-analysis.spec.ts:4:3 Activities Page - Desktop and Mobile Screenshots" type="FAILURE">
<![CDATA[  [firefox] › systematic-page-analysis.spec.ts:4:3 › Systematic Page Analysis - 2025 Best Practices › Activities Page - Desktop and Mobile Screenshots 

    Error: page.goto: NS_ERROR_CONNECTION_REFUSED
    Call log:
      - navigating to "http://localhost:5174/activities", waiting until "load"


       7 |     // Desktop screenshot
       8 |     await page.setViewportSize({ width: 1200, height: 800 });
    >  9 |     await page.goto('http://localhost:5174/activities');
         |                ^
      10 |     await page.waitForLoadState('networkidle');
      11 |     await page.waitForTimeout(2000); // Wait for any animations
      12 |     
        at E:\Augment Code Testing\sri-lanka-tourist-guide\tests\systematic-page-analysis.spec.ts:9:16

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    test-results\systematic-page-analysis-S-2637b-ktop-and-Mobile-Screenshots-firefox\test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    test-results\systematic-page-analysis-S-2637b-ktop-and-Mobile-Screenshots-firefox\video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ..\test-results\systematic-page-analysis-S-2637b-ktop-and-Mobile-Screenshots-firefox\error-context.md
]]>
</failure>
<system-out>
<![CDATA[📸 Taking Activities page screenshots for analysis...

[[ATTACHMENT|systematic-page-analysis-S-2637b-ktop-and-Mobile-Screenshots-firefox\test-failed-1.png]]

[[ATTACHMENT|systematic-page-analysis-S-2637b-ktop-and-Mobile-Screenshots-firefox\video.webm]]

[[ATTACHMENT|systematic-page-analysis-S-2637b-ktop-and-Mobile-Screenshots-firefox\error-context.md]]
]]>
</system-out>
</testcase>
</testsuite>
<testsuite name="systematic-page-analysis.spec.ts" timestamp="2025-06-25T04:56:21.460Z" hostname="webkit" tests="1" failures="1" skipped="0" time="4.532" errors="0">
<testcase name="Systematic Page Analysis - 2025 Best Practices › Activities Page - Desktop and Mobile Screenshots" classname="systematic-page-analysis.spec.ts" time="4.532">
<failure message="systematic-page-analysis.spec.ts:4:3 Activities Page - Desktop and Mobile Screenshots" type="FAILURE">
<![CDATA[  [webkit] › systematic-page-analysis.spec.ts:4:3 › Systematic Page Analysis - 2025 Best Practices › Activities Page - Desktop and Mobile Screenshots 

    Error: page.goto: Could not connect to server
    Call log:
      - navigating to "http://localhost:5174/activities", waiting until "load"


       7 |     // Desktop screenshot
       8 |     await page.setViewportSize({ width: 1200, height: 800 });
    >  9 |     await page.goto('http://localhost:5174/activities');
         |                ^
      10 |     await page.waitForLoadState('networkidle');
      11 |     await page.waitForTimeout(2000); // Wait for any animations
      12 |     
        at E:\Augment Code Testing\sri-lanka-tourist-guide\tests\systematic-page-analysis.spec.ts:9:16

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    test-results\systematic-page-analysis-S-2637b-ktop-and-Mobile-Screenshots-webkit\test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    test-results\systematic-page-analysis-S-2637b-ktop-and-Mobile-Screenshots-webkit\video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────
]]>
</failure>
<system-out>
<![CDATA[📸 Taking Activities page screenshots for analysis...

[[ATTACHMENT|systematic-page-analysis-S-2637b-ktop-and-Mobile-Screenshots-webkit\test-failed-1.png]]

[[ATTACHMENT|systematic-page-analysis-S-2637b-ktop-and-Mobile-Screenshots-webkit\video.webm]]
]]>
</system-out>
</testcase>
</testsuite>
<testsuite name="systematic-page-analysis.spec.ts" timestamp="2025-06-25T04:56:21.460Z" hostname="Mobile Chrome" tests="1" failures="1" skipped="0" time="4.186" errors="0">
<testcase name="Systematic Page Analysis - 2025 Best Practices › Activities Page - Desktop and Mobile Screenshots" classname="systematic-page-analysis.spec.ts" time="4.186">
<failure message="systematic-page-analysis.spec.ts:4:3 Activities Page - Desktop and Mobile Screenshots" type="FAILURE">
<![CDATA[  [Mobile Chrome] › systematic-page-analysis.spec.ts:4:3 › Systematic Page Analysis - 2025 Best Practices › Activities Page - Desktop and Mobile Screenshots 

    Error: page.goto: net::ERR_CONNECTION_REFUSED at http://localhost:5174/activities
    Call log:
      - navigating to "http://localhost:5174/activities", waiting until "load"


       7 |     // Desktop screenshot
       8 |     await page.setViewportSize({ width: 1200, height: 800 });
    >  9 |     await page.goto('http://localhost:5174/activities');
         |                ^
      10 |     await page.waitForLoadState('networkidle');
      11 |     await page.waitForTimeout(2000); // Wait for any animations
      12 |     
        at E:\Augment Code Testing\sri-lanka-tourist-guide\tests\systematic-page-analysis.spec.ts:9:16

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    test-results\systematic-page-analysis-S-2637b-ktop-and-Mobile-Screenshots-Mobile-Chrome\test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    test-results\systematic-page-analysis-S-2637b-ktop-and-Mobile-Screenshots-Mobile-Chrome\video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ..\test-results\systematic-page-analysis-S-2637b-ktop-and-Mobile-Screenshots-Mobile-Chrome\error-context.md
]]>
</failure>
<system-out>
<![CDATA[📸 Taking Activities page screenshots for analysis...

[[ATTACHMENT|systematic-page-analysis-S-2637b-ktop-and-Mobile-Screenshots-Mobile-Chrome\test-failed-1.png]]

[[ATTACHMENT|systematic-page-analysis-S-2637b-ktop-and-Mobile-Screenshots-Mobile-Chrome\video.webm]]

[[ATTACHMENT|systematic-page-analysis-S-2637b-ktop-and-Mobile-Screenshots-Mobile-Chrome\error-context.md]]
]]>
</system-out>
</testcase>
</testsuite>
<testsuite name="systematic-page-analysis.spec.ts" timestamp="2025-06-25T04:56:21.460Z" hostname="Mobile Safari" tests="1" failures="1" skipped="0" time="5.755" errors="0">
<testcase name="Systematic Page Analysis - 2025 Best Practices › Activities Page - Desktop and Mobile Screenshots" classname="systematic-page-analysis.spec.ts" time="5.755">
<failure message="systematic-page-analysis.spec.ts:4:3 Activities Page - Desktop and Mobile Screenshots" type="FAILURE">
<![CDATA[  [Mobile Safari] › systematic-page-analysis.spec.ts:4:3 › Systematic Page Analysis - 2025 Best Practices › Activities Page - Desktop and Mobile Screenshots 

    Error: page.goto: Could not connect to server
    Call log:
      - navigating to "http://localhost:5174/activities", waiting until "load"


       7 |     // Desktop screenshot
       8 |     await page.setViewportSize({ width: 1200, height: 800 });
    >  9 |     await page.goto('http://localhost:5174/activities');
         |                ^
      10 |     await page.waitForLoadState('networkidle');
      11 |     await page.waitForTimeout(2000); // Wait for any animations
      12 |     
        at E:\Augment Code Testing\sri-lanka-tourist-guide\tests\systematic-page-analysis.spec.ts:9:16

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    test-results\systematic-page-analysis-S-2637b-ktop-and-Mobile-Screenshots-Mobile-Safari\test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    test-results\systematic-page-analysis-S-2637b-ktop-and-Mobile-Screenshots-Mobile-Safari\video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────
]]>
</failure>
<system-out>
<![CDATA[📸 Taking Activities page screenshots for analysis...

[[ATTACHMENT|systematic-page-analysis-S-2637b-ktop-and-Mobile-Screenshots-Mobile-Safari\test-failed-1.png]]

[[ATTACHMENT|systematic-page-analysis-S-2637b-ktop-and-Mobile-Screenshots-Mobile-Safari\video.webm]]
]]>
</system-out>
</testcase>
</testsuite>
</testsuites>