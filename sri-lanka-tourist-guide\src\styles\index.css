/* Leaflet CSS */
@import 'leaflet/dist/leaflet.css';

@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  /* 2025 CSS Custom Properties - Optimized Color Palette with WCAG Compliance */
  :root {
    /* 2025 Primary Colors - High Contrast & Accessible */
    --primary-coral: #e74c3c;        /* 4.5:1 contrast ratio on white */
    --primary-teal: #16a085;         /* 4.5:1 contrast ratio on white */
    --primary-navy: #2c3e50;         /* 12.6:1 contrast ratio on white */
    --primary-orange: #e67e22;       /* 4.5:1 contrast ratio on white */

    /* 2025 Secondary Colors - Balanced & Modern */
    --secondary-sage: #7fb069;       /* 4.5:1 contrast ratio on white */
    --secondary-lavender: #9b59b6;   /* 4.5:1 contrast ratio on white */
    --secondary-amber: #f39c12;      /* 3.1:1 contrast ratio on white - large text only */
    --secondary-slate: #34495e;      /* 9.6:1 contrast ratio on white */

    /* 2025 Neutral Colors - Perfect for Backgrounds */
    --neutral-white: #ffffff;
    --neutral-light: #f8f9fa;        /* Softer than pure white */
    --neutral-cream: #fefefe;        /* Warm white alternative */
    --neutral-gray-100: #f1f3f4;
    --neutral-gray-200: #e8eaed;
    --neutral-gray-300: #dadce0;
    --neutral-gray-400: #9aa0a6;
    --neutral-gray-500: #5f6368;     /* 7.0:1 contrast ratio on white */
    --neutral-gray-600: #3c4043;     /* 11.9:1 contrast ratio on white */
    --neutral-gray-700: #202124;     /* 16.1:1 contrast ratio on white */

    /* 2025 Accent Colors - Vibrant but Accessible */
    --accent-success: #27ae60;       /* 4.5:1 contrast ratio */
    --accent-warning: #e67e22;       /* 4.5:1 contrast ratio */
    --accent-error: #e74c3c;         /* 4.5:1 contrast ratio */
    --accent-info: #3498db;          /* 4.5:1 contrast ratio */

    /* 2025 Pastel Backgrounds - Subtle & Elegant */
    --bg-pastel-coral: #fdf2f2;     /* Very light coral background */
    --bg-pastel-teal: #f0f9f7;      /* Very light teal background */
    --bg-pastel-lavender: #f8f4ff;  /* Very light lavender background */
    --bg-pastel-sage: #f6faf8;      /* Very light sage background */
    --bg-pastel-amber: #fef9f0;     /* Very light amber background */

    /* 2025 Gradients - Subtle & Professional */
    --gradient-warm: linear-gradient(135deg, var(--bg-pastel-coral) 0%, var(--bg-pastel-amber) 100%);
    --gradient-cool: linear-gradient(135deg, var(--bg-pastel-teal) 0%, var(--bg-pastel-lavender) 100%);
    --gradient-nature: linear-gradient(135deg, var(--bg-pastel-sage) 0%, var(--bg-pastel-teal) 100%);
    --gradient-sunset: linear-gradient(135deg, var(--bg-pastel-amber) 0%, var(--bg-pastel-coral) 100%);

    /* Enhanced Shadows - 2025 Professional Style */
    --shadow-subtle: 0 2px 8px rgba(0, 0, 0, 0.04);
    --shadow-soft: 0 4px 16px rgba(0, 0, 0, 0.06);
    --shadow-medium: 0 8px 24px rgba(0, 0, 0, 0.08);
    --shadow-strong: 0 16px 32px rgba(0, 0, 0, 0.12);
    --shadow-focus: 0 0 0 3px rgba(52, 152, 219, 0.2);
  }

  html {
    scroll-behavior: smooth;
  }

  body {
    font-family: 'Inter', system-ui, sans-serif;
    line-height: 1.6;
    color: #1f2937;
    font-size: 16px; /* Ensure minimum 16px base font size */
  }

  /* Ensure paragraphs have adequate size */
  p {
    font-size: 16px;
    line-height: 1.6;
  }

  h1, h2, h3, h4, h5, h6 {
    font-family: 'Playfair Display', serif;
    font-weight: 600;
    line-height: 1.2;
  }

  /* Ensure minimum heading sizes for accessibility */
  h1 {
    @apply text-3xl sm:text-4xl lg:text-5xl;
  }

  h2 {
    @apply text-2xl sm:text-3xl lg:text-4xl;
  }

  h3 {
    @apply text-xl sm:text-2xl lg:text-3xl;
  }

  h4 {
    @apply text-lg sm:text-xl lg:text-2xl;
  }

  h5 {
    @apply text-base sm:text-lg lg:text-xl;
  }

  h6 {
    @apply text-base sm:text-lg;
  }

  /* Ensure all pages start from top */
  .page-container {
    scroll-margin-top: 0;
  }
}

@layer components {
  /* 2025 Accessible Button Design - WCAG Compliant */
  .btn-primary {
    background: var(--primary-coral);
    color: var(--neutral-white);
    @apply font-semibold py-4 px-8 md:py-3 md:px-6 rounded-2xl transition-all duration-300 transform hover:scale-105 focus:outline-none min-h-[48px] md:min-h-[44px] text-base md:text-sm;
    box-shadow: var(--shadow-soft);
    border: 2px solid transparent;
  }

  .btn-primary:hover {
    background: var(--primary-orange);
    box-shadow: var(--shadow-medium);
  }

  .btn-primary:focus {
    box-shadow: var(--shadow-focus);
    border-color: var(--accent-info);
  }

  .btn-secondary {
    background: var(--primary-teal);
    color: var(--neutral-white);
    @apply font-semibold py-4 px-8 md:py-3 md:px-6 rounded-2xl transition-all duration-300 transform hover:scale-105 focus:outline-none min-h-[48px] md:min-h-[44px] text-base md:text-sm;
    box-shadow: var(--shadow-soft);
    border: 2px solid transparent;
  }

  .btn-secondary:hover {
    background: var(--secondary-lavender);
    box-shadow: var(--shadow-medium);
  }

  .btn-secondary:focus {
    box-shadow: var(--shadow-focus);
    border-color: var(--accent-info);
  }

  /* 2025 Glass Morphism Button - Accessible */
  .btn-glass {
    background: var(--neutral-white);
    color: var(--neutral-gray-700);
    @apply font-semibold py-4 px-8 md:py-3 md:px-6 rounded-2xl border-2 transition-all duration-300 transform hover:scale-105 focus:outline-none min-h-[48px] md:min-h-[44px] text-base md:text-sm;
    box-shadow: var(--shadow-soft);
    border-color: var(--neutral-gray-300);
    backdrop-filter: blur(8px);
  }

  .btn-glass:hover {
    background: var(--bg-pastel-teal);
    border-color: var(--primary-teal);
    box-shadow: var(--shadow-medium);
  }

  .btn-glass:focus {
    box-shadow: var(--shadow-focus);
    border-color: var(--accent-info);
  }

  /* 2025 Accessible Card Design */
  .card {
    background: var(--neutral-white);
    @apply rounded-3xl transition-all duration-300 transform hover:-translate-y-1 mx-4 md:mx-0;
    box-shadow: var(--shadow-soft);
    border: 1px solid var(--neutral-gray-200);
  }

  .card:hover {
    background: var(--bg-pastel-teal);
    box-shadow: var(--shadow-medium);
    border-color: var(--primary-teal);
  }

  /* 2025 Glass Card with Accessible Colors */
  .glass-card {
    background: var(--neutral-light);
    @apply rounded-3xl transition-all duration-300 transform hover:scale-[1.02];
    box-shadow: var(--shadow-soft);
    border: 1px solid var(--neutral-gray-200);
  }

  .glass-card:hover {
    background: var(--bg-pastel-lavender);
    box-shadow: var(--shadow-medium);
    border-color: var(--secondary-lavender);
  }

  /* 2025 Luxury Accessible Card */
  .luxury-card {
    background: var(--gradient-warm);
    @apply rounded-3xl transition-all duration-300 transform hover:-translate-y-2;
    box-shadow: var(--shadow-soft);
    border: 1px solid var(--neutral-gray-200);
  }

  .luxury-card:hover {
    background: var(--gradient-sunset);
    box-shadow: var(--shadow-medium);
  }

  /* Mobile-first section padding */
  .section-padding {
    @apply py-8 px-4 sm:py-12 sm:px-6 md:py-16 lg:px-8;
  }

  /* Enhanced mobile spacing with better touch targets */
  .mobile-spacing {
    @apply px-4 py-8 sm:px-6 sm:py-10 lg:px-8 lg:py-12;
  }

  /* Content spacing optimized for mobile reading */
  .content-spacing {
    @apply space-y-8 sm:space-y-10 lg:space-y-12;
  }

  /* 2025 Animation Classes */
  .animate-delay-1 {
    animation-delay: 1s;
  }

  .animate-delay-2 {
    animation-delay: 2s;
  }

  .animate-delay-3 {
    animation-delay: 3s;
  }

  /* 2025 Accessible Text Effects - WCAG Compliant */
  .text-gradient-warm {
    background: linear-gradient(135deg, var(--primary-coral) 0%, var(--primary-orange) 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    filter: drop-shadow(0 1px 2px rgba(0,0,0,0.1));
  }

  .text-gradient-cool {
    background: linear-gradient(135deg, var(--primary-teal) 0%, var(--secondary-lavender) 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    filter: drop-shadow(0 1px 2px rgba(0,0,0,0.1));
  }

  .text-gradient-nature {
    background: linear-gradient(135deg, var(--secondary-sage) 0%, var(--primary-teal) 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    filter: drop-shadow(0 1px 2px rgba(0,0,0,0.1));
  }

  /* 2025 Solid Color Text - High Contrast Alternative */
  .text-primary {
    color: var(--primary-navy);
  }

  .text-secondary {
    color: var(--neutral-gray-600);
  }

  .text-accent {
    color: var(--primary-coral);
  }

  /* 2025 Enhanced Shadows */
  .shadow-glow {
    box-shadow: var(--shadow-glow);
  }

  .shadow-neon {
    box-shadow: var(--shadow-neon);
  }

  .shadow-soft {
    box-shadow: var(--shadow-soft);
  }

  /* Mobile-first container with better margins */
  .container-max {
    @apply max-w-7xl mx-auto px-4 sm:px-6 lg:px-8;
  }

  /* Consistent Brand Gradient Text Effects - 2025 */
  .text-gradient {
    @apply bg-gradient-to-r from-primary-600 via-primary-500 to-secondary-600 bg-clip-text text-transparent;
  }

  .text-gradient-luxury {
    @apply bg-gradient-to-r from-gray-900 via-gray-800 to-gray-900 bg-clip-text text-transparent;
  }

  .text-gradient-gold {
    @apply bg-gradient-to-r from-amber-600 via-yellow-500 to-orange-600 bg-clip-text text-transparent;
  }

  .text-gradient-brand {
    @apply bg-gradient-to-r from-primary-600 to-accent-600 bg-clip-text text-transparent;
  }

  .text-gradient-warm {
    @apply bg-gradient-to-r from-orange-500 via-red-500 to-pink-500 bg-clip-text text-transparent;
  }

  /* Consistent Brand Background Gradients - 2025 */
  .hero-gradient {
    @apply bg-gradient-to-br from-primary-50 via-white to-accent-50;
  }

  .luxury-gradient {
    @apply bg-gradient-to-br from-gray-50 via-white to-primary-50;
  }

  .glass-gradient {
    @apply bg-gradient-to-br from-white/20 via-white/10 to-transparent;
  }

  .brand-gradient {
    @apply bg-gradient-to-br from-primary-500 to-accent-500;
  }

  .warm-gradient {
    @apply bg-gradient-to-br from-orange-400 via-red-400 to-pink-400;
  }

  .cool-gradient {
    @apply bg-gradient-to-br from-primary-400 via-blue-400 to-accent-400;
  }

  .overlay-gradient {
    @apply bg-gradient-to-t from-black/60 via-black/20 to-transparent;
  }

  .premium-overlay {
    @apply bg-gradient-to-t from-black/80 via-black/40 to-transparent;
  }

  /* 2025 Mobile-first touch targets with improved accessibility */
  .touch-target {
    @apply min-h-[48px] min-w-[48px] md:min-h-[44px] md:min-w-[44px] flex items-center justify-center;
  }

  /* Enhanced mobile touch targets for 2025 */
  .touch-target-large {
    @apply min-h-[56px] min-w-[56px] md:min-h-[48px] md:min-w-[48px] flex items-center justify-center;
  }

  /* Mobile-optimized spacing */
  .mobile-safe-area {
    @apply pb-safe-bottom pl-safe-left pr-safe-right;
  }

  /* 2025 Mobile content optimization */
  .mobile-content {
    @apply px-4 py-6 md:px-6 md:py-8 lg:px-8 lg:py-12;
  }

  /* Mobile-first navigation */
  .mobile-nav-item {
    @apply block w-full text-left py-4 px-4 text-base font-medium rounded-lg transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2;
  }

  /* Mobile-first form elements */
  .form-input {
    @apply w-full px-4 py-4 md:py-3 text-base md:text-sm border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500 transition-colors duration-200;
  }

  /* 2025 Mobile-first text sizing with improved readability */
  .mobile-text-sm {
    @apply text-sm md:text-base lg:text-lg;
  }

  .mobile-text-base {
    @apply text-base md:text-lg lg:text-xl;
  }

  .mobile-text-lg {
    @apply text-lg md:text-xl lg:text-2xl;
  }

  .mobile-text-xl {
    @apply text-xl md:text-2xl lg:text-3xl;
  }

  .mobile-text-2xl {
    @apply text-2xl md:text-3xl lg:text-4xl;
  }

  .mobile-text-3xl {
    @apply text-3xl md:text-4xl lg:text-5xl;
  }

  /* Mobile-optimized line heights for better readability */
  .mobile-leading {
    @apply leading-relaxed md:leading-normal;
  }

  /* Mobile-first spacing utilities */
  .mobile-space-y {
    @apply space-y-4 md:space-y-6 lg:space-y-8;
  }

  /* Consistent Brand Color Utilities - 2025 */
  .text-brand-primary {
    @apply text-primary-600;
  }

  .text-brand-secondary {
    @apply text-secondary-600;
  }

  .text-brand-accent {
    @apply text-accent-600;
  }

  .bg-brand-primary {
    @apply bg-primary-600;
  }

  .bg-brand-secondary {
    @apply bg-secondary-600;
  }

  .bg-brand-accent {
    @apply bg-accent-600;
  }

  .border-brand-primary {
    @apply border-primary-600;
  }

  .border-brand-secondary {
    @apply border-secondary-600;
  }

  .border-brand-accent {
    @apply border-accent-600;
  }

  /* Consistent Status Colors */
  .text-success {
    @apply text-green-600;
  }

  .text-warning {
    @apply text-yellow-600;
  }

  .text-error {
    @apply text-red-600;
  }

  .text-info {
    @apply text-blue-600;
  }

  .bg-success {
    @apply bg-green-100 text-green-800;
  }

  .bg-warning {
    @apply bg-yellow-100 text-yellow-800;
  }

  .bg-error {
    @apply bg-red-100 text-red-800;
  }

  .bg-info {
    @apply bg-blue-100 text-blue-800;
  }
}

@layer utilities {
  .animation-delay-200 {
    animation-delay: 200ms;
  }
  
  .animation-delay-400 {
    animation-delay: 400ms;
  }
  
  .animation-delay-600 {
    animation-delay: 600ms;
  }
  
  .animation-delay-800 {
    animation-delay: 800ms;
  }
  
  .animation-delay-1000 {
    animation-delay: 1000ms;
  }
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: #f1f5f9;
}

::-webkit-scrollbar-thumb {
  background: #cbd5e1;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: #94a3b8;
}

/* Loading animation */
.loading-spinner {
  border: 3px solid #f3f4f6;
  border-top: 3px solid #3b82f6;
  border-radius: 50%;
  width: 40px;
  height: 40px;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* GSAP animations will be applied via JavaScript */
.gsap-fade-in {
  opacity: 0;
  transform: translateY(30px);
}

.gsap-slide-left {
  opacity: 0;
  transform: translateX(-50px);
}

.gsap-slide-right {
  opacity: 0;
  transform: translateX(50px);
}

.gsap-scale-in {
  opacity: 0;
  transform: scale(0.8);
}

/* Loading spinner */
.loading-spinner {
  width: 40px;
  height: 40px;
  border: 4px solid #f3f4f6;
  border-top: 4px solid #8b5cf6;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Leaflet Map Customizations */
.leaflet-map-container {
  height: 100% !important;
  width: 100% !important;
  min-height: 400px !important;
}

.map-container {
  height: 500px;
  min-height: 400px;
  width: 100%;
  position: relative;
}

.map-container-full {
  height: 100%;
  min-height: 400px;
  width: 100%;
  position: relative;
}

.map-container-dynamic {
  min-height: 400px;
  width: 100%;
  position: relative;
  display: block;
  overflow: hidden;
}

/* Specific height classes for different map sizes */
.map-height-500 {
  height: 500px !important;
}

.map-height-400 {
  height: 400px !important;
}

.map-height-600 {
  height: 600px !important;
}

/* Ensure map container has proper dimensions */
.leaflet-container {
  font-family: 'Inter', system-ui, sans-serif;
  height: 100% !important;
  width: 100% !important;
  z-index: 1;
}

/* Fix for map display issues */
.leaflet-map-pane {
  z-index: 2;
}

.leaflet-tile-pane {
  z-index: 1;
}

.leaflet-popup-content-wrapper {
  border-radius: 8px;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
}

.leaflet-popup-content {
  margin: 0;
  line-height: 1.5;
}

.custom-popup .leaflet-popup-content-wrapper {
  background: white;
  border-radius: 12px;
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
}

.custom-popup .leaflet-popup-tip {
  background: white;
}

/* Map controls styling */
.leaflet-control-zoom a {
  background-color: white;
  border: 1px solid #e5e7eb;
  color: #374151;
}

.leaflet-control-zoom a:hover {
  background-color: #f9fafb;
  border-color: #d1d5db;
}

/* Fix for map tiles not loading properly */
.leaflet-tile-container {
  pointer-events: auto;
}

.leaflet-tile {
  pointer-events: auto;
}

/* Ensure map container has proper dimensions and display */
.leaflet-container {
  background: #f8f9fa;
  outline: none;
}

.leaflet-map-container .leaflet-container {
  height: 100% !important;
  width: 100% !important;
  position: relative;
  z-index: 1;
}

/* Fix for map not displaying properly */
.map-container-dynamic .leaflet-container {
  height: 100% !important;
  min-height: 400px !important;
}

/* Article Content Styling - 2025 Enhanced with Proper Spacing */
.article-content,
.markdown-content {
  line-height: 1.8;
}

.article-content h1,
.article-content h2,
.article-content h3,
.article-content h4,
.article-content h5,
.article-content h6,
.markdown-content h1,
.markdown-content h2,
.markdown-content h3,
.markdown-content h4,
.markdown-content h5,
.markdown-content h6 {
  scroll-margin-top: 100px; /* Account for sticky header */
  margin-top: 2.5rem;
  margin-bottom: 1.5rem;
  font-weight: 600;
  color: var(--primary-navy);
}

.article-content h1,
.markdown-content h1 {
  @apply text-3xl md:text-4xl;
  margin-top: 3rem;
  margin-bottom: 2rem;
}

.article-content h2,
.markdown-content h2 {
  @apply text-2xl md:text-3xl;
  margin-top: 2.5rem;
  margin-bottom: 1.5rem;
  padding-bottom: 0.75rem;
  border-bottom: 2px solid var(--bg-pastel-teal);
}

.article-content h3,
.markdown-content h3 {
  @apply text-xl md:text-2xl;
  margin-top: 2rem;
  margin-bottom: 1rem;
}

.article-content h4,
.markdown-content h4 {
  @apply text-lg md:text-xl;
  margin-top: 1.5rem;
  margin-bottom: 0.75rem;
}

.article-content h5,
.markdown-content h5 {
  @apply text-base md:text-lg;
  margin-top: 1.25rem;
  margin-bottom: 0.5rem;
}

.article-content h6,
.markdown-content h6 {
  @apply text-sm md:text-base;
  margin-top: 1rem;
  margin-bottom: 0.5rem;
}

.article-content p,
.markdown-content p {
  margin-bottom: 1.5rem;
  line-height: 1.8;
  color: var(--neutral-gray-700);
  font-size: 1.1rem;
}

.article-content ul,
.article-content ol,
.markdown-content ul,
.markdown-content ol {
  margin-bottom: 1.5rem;
  padding-left: 2rem;
}

.article-content li,
.markdown-content li {
  margin-bottom: 0.75rem;
  line-height: 1.7;
  color: var(--neutral-gray-700);
}

.article-content blockquote,
.markdown-content blockquote {
  position: relative;
  margin: 2rem 0;
  padding: 1.5rem 2rem;
  background: var(--bg-pastel-teal);
  border-left: 4px solid var(--primary-teal);
  border-radius: 0.75rem;
  font-style: italic;
  color: var(--neutral-gray-700);
}

.article-content blockquote::before,
.markdown-content blockquote::before {
  content: '"';
  position: absolute;
  left: 1rem;
  top: 0.5rem;
  font-size: 3rem;
  color: var(--primary-teal);
  font-family: serif;
  opacity: 0.3;
}

.article-content pre,
.markdown-content pre {
  font-family: 'Fira Code', 'Monaco', 'Consolas', monospace;
  margin: 2rem 0;
  padding: 1.5rem;
  background: var(--neutral-gray-900);
  color: var(--neutral-white);
  border-radius: 0.75rem;
  overflow-x: auto;
}

.article-content code,
.markdown-content code {
  background: var(--bg-pastel-coral);
  color: var(--primary-coral);
  padding: 0.25rem 0.5rem;
  border-radius: 0.375rem;
  font-size: 0.9em;
}

.article-content pre code,
.markdown-content pre code {
  background: transparent;
  color: inherit;
  padding: 0;
}

.article-content table,
.markdown-content table {
  width: 100%;
  margin: 2rem 0;
  border-collapse: collapse;
  font-size: 0.95rem;
}

.article-content th,
.article-content td,
.markdown-content th,
.markdown-content td {
  padding: 0.75rem 1rem;
  border: 1px solid var(--neutral-gray-300);
  text-align: left;
}

.article-content th,
.markdown-content th {
  background: var(--bg-pastel-teal);
  font-weight: 600;
  color: var(--primary-navy);
}

.article-content img,
.markdown-content img {
  margin: 2rem 0;
  border-radius: 0.75rem;
  box-shadow: var(--shadow-soft);
  transition: transform 0.3s ease;
  max-width: 100%;
  height: auto;
}

.article-content img:hover,
.markdown-content img:hover {
  transform: scale(1.02);
}

.article-content hr,
.markdown-content hr {
  margin: 3rem 0;
  border: none;
  height: 2px;
  background: linear-gradient(to right, transparent, var(--primary-teal), transparent);
}

.article-content strong,
.markdown-content strong {
  font-weight: 600;
  color: var(--primary-navy);
}

.article-content em,
.markdown-content em {
  font-style: italic;
  color: var(--neutral-gray-600);
}

/* Smooth scrolling for anchor links */
html {
  scroll-behavior: smooth;
}

/* Line clamp utilities */
.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.line-clamp-3 {
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

/* Hide scrollbars for horizontal scroll */
.scrollbar-hide {
  -ms-overflow-style: none;
  scrollbar-width: none;
}

.scrollbar-hide::-webkit-scrollbar {
  display: none;
}
